{"firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log"]}], "hosting": {"public": "build/web", "rewrites": [{"source": "**", "destination": "/index.html"}], "ignore": ["firebase.json", "**/.*", "**/node_modules/**"]}, "storage": {"rules": "storage.rules"}}