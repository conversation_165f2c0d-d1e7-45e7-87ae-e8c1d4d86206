class CartCourseModel {
  late final String courseId;
  late int qty;

  CartCourseModel({required this.courseId, this.qty = 1});

  CartCourseModel.fromJson(Map<String, dynamic> json) {
    courseId = json.keys.first;
    qty = json[json.keys.first];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data[courseId] = qty;
    return data;
  }

  @override
  bool operator ==(Object other) {
    if (other is CartCourseModel) {
      return courseId == other.courseId;
    }
    return false;
  }

  @override
  int get hashCode => courseId.hashCode;
}
