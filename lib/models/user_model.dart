import 'cart_model.dart';

class UserModel {
  UserModel({
    required this.docId,
    required this.contact,
    required this.email,
    required this.name,
    required this.branch,
    required this.eId,
    required this.cartItems,
    required this.createdBy,
  });
  late final String docId;
  late final String contact;
  late final String email;
  late final String name;
  late final String branch;
  late final String? eId;
  late final String? createdBy;
  late final List<CartCourseModel> cartItems;

  UserModel.fromJson(String id, Map<String, dynamic> json) {
    docId = id;
    contact = json['contact'];
    email = json['email'];
    name = json['name'];
    branch = json['branch'];
    createdBy = json['createdBy'];
    eId = json['eId'];
    cartItems = Map.castFrom(json['cartItems'])
        .entries
        .map((e) => CartCourseModel.fromJson({e.key: e.value}))
        .toList();
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['contact'] = contact;
    data['email'] = email;
    data['createdBy'] = createdBy;
    data['name'] = name;
    data['branch'] = branch;
    data['eId'] = eId;
    data['cartItems'] = cartItems.map((e) => e.toJson()).toList();
    return data;
  }
}
