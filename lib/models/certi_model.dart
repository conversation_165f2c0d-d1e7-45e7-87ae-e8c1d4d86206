import 'package:cloud_firestore/cloud_firestore.dart';

class CertiModel {
  final String docId;
  final String fullName;
  final String courseName;
  final Timestamp courseLastUpdated;
  final Timestamp completedOn;
  final int validForDays;
  final String hours;
  final String uid;
  final String uCourseId;
  final String courseId;
  final String score;

  CertiModel({
    required this.fullName,
    required this.courseName,
    required this.courseLastUpdated,
    required this.completedOn,
    required this.validForDays,
    required this.hours,
    required this.uid,
    required this.uCourseId,
    required this.courseId,
    required this.score,
    required this.docId,
  });
  static CertiModel fromSnap(DocumentSnapshot<Map<String, dynamic>> value) {
    return CertiModel(
      docId: value.id,
      fullName: value['fullName'],
      courseName: value['courseName'],
      courseLastUpdated: value['courseLastUpdated'],
      completedOn: value['completedOn'],
      validForDays: value['validForDays'],
      hours: value['hours'],
      uid: value['uid'],
      uCourseId: value['uCourseId'],
      courseId: value['courseId'],
      score: value['score'],
    );
  }
}
