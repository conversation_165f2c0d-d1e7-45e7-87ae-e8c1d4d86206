import 'package:cloud_firestore/cloud_firestore.dart';

class UserCourseModel {
  UserCourseModel({
    required this.docId,
    required this.endDate,
    required this.time,
    required this.examSchedules,
    required this.orderId,
    required this.score,
    required this.assigned,
    required this.certiUrl,
    required this.courseId,
    required this.startDate,
    required this.assignedByName,
    required this.progress,
    required this.asignScores,
    required this.assignedByUid,
    required this.blocked,
    required this.uid,
    required this.qty,
    required this.completed,
    required this.isEnterprise,
    required this.completedOn,
  });
  late final String docId;
  late final Timestamp? endDate;
  late final Timestamp? time;
  late final Map<String, dynamic> examSchedules;
  late final String? orderId;
  late final String? score;
  late final int assigned;
  late final String? certiUrl;
  late final String courseId;
  late final Timestamp? startDate;
  late final String? assignedByName;
  late final Map<String, dynamic> progress;
  late final Map<String, dynamic> asignScores;
  late final String? assignedByUid;
  late final bool blocked;
  late final bool completed;
  late final bool isEnterprise;
  late final String uid;
  late final int qty;
  late final Timestamp? completedOn;

  UserCourseModel.fromJson(String id, Map<String, dynamic> json) {
    docId = id;
    endDate = json['endDate'];
    time = json['time'];
    examSchedules = json['examSchedules'];
    orderId = json['orderId'];
    score = json['score'];
    assigned = json['assigned'];
    certiUrl = json['certiUrl'];
    courseId = json['courseId'];
    startDate = json['startDate'];
    assignedByName = json['assignedByName'];
    progress = json['progress'];
    asignScores = json['asignScores'];
    assignedByUid = json['assignedByUid'];
    blocked = json['blocked'];
    uid = json['uid'];
    qty = json['qty'];
    completed = json['completed'];
    isEnterprise = json['isEnterprise'];
    completedOn = json['completedOn'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['endDate'] = endDate;
    data['time'] = time;
    data['examSchedules'] = examSchedules;
    data['orderId'] = orderId;
    data['score'] = score;
    data['assigned'] = assigned;
    data['certiUrl'] = certiUrl;
    data['courseId'] = courseId;
    data['startDate'] = startDate;
    data['assignedByName'] = assignedByName;
    data['progress'] = progress;
    data['asignScores'] = asignScores;
    data['assignedByUid'] = assignedByUid;
    data['blocked'] = blocked;
    data['uid'] = uid;
    data['qty'] = qty;
    data['completed'] = completed;
    data['isEnterprise'] = isEnterprise;
    data['completedOn'] = completedOn;
    return data;
  }
}

class ExamSchedule {
  late final String id;
  late final Timestamp date;
  late final Timestamp scheduledOn;
  late final String schDocId;
  late final String courseId;
  late final String uCourseId;
  late final String uid;
  late final bool passed;
  late final bool canReSchedule;

  ExamSchedule({
    required this.id,
    required this.date,
    required this.scheduledOn,
    required this.schDocId,
    required this.courseId,
    required this.uCourseId,
    required this.uid,
    required this.passed,
    required this.canReSchedule,
  });

  ExamSchedule.fromJson(Map<String, dynamic> json) {
    id = json['sid'];
    date = json['date'];
    scheduledOn = json['scheduledOn'];
    schDocId = json['schDocId'];
    courseId = json['courseId'];
    uCourseId = json['uCourseId'];
    uid = json['uid'];
    passed = json['passed'];
    canReSchedule = json['canReSchedule'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['date'] = date;
    data['schDocId'] = schDocId;
    data['scheduledOn'] = scheduledOn;
    data['courseId'] = courseId;
    data['uCourseId'] = uCourseId;
    data['uid'] = uid;
    data['passed'] = passed;
    data['canReSchedule'] = canReSchedule;
    return data;
  }
}

// class Progress {
//   late final String id;
//   late final String status;

//   Progress.fromJson(Map json) {
//     id = json['id'];
//     status = json['status'];
//   }

//   Map<String, dynamic> toJson() {
//     final data = <String, dynamic>{};
//     return data;
//   }
// }

class ProgressType {
  static const completed = "Completed";
  static const inProgress = "In Progress";
}
