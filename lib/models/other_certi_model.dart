import 'package:cloud_firestore/cloud_firestore.dart';

class OtherCertiModel {
  final String docId;
  final String title;
  final String fullname;
  final DateTime addedOn;
  final String addedByUid;
  final String url;
  final String uid;

  OtherCertiModel({
    required this.docId,
    required this.title,
    required this.fullname,
    required this.addedByUid,
    required this.addedOn,
    required this.url,
    required this.uid,
  });

  static List<OtherCertiModel> toCertiList(
          QuerySnapshot<Map<String, dynamic>> event) =>
      event.docs
          .map(
            (e) => OtherCertiModel(
              docId: e.id,
              title: e['title'],
              addedByUid: e['addedByUid'],
              addedOn: e['addedOn'].toDate(),
              fullname: e['fullname'],
              url: e['url'],
              uid: e['uid'],
            ),
          )
          .toList();
}
