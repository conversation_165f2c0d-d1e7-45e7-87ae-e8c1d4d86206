import 'package:cloud_firestore/cloud_firestore.dart';

class ScheduleModel {
  final String docId;
  final String title;
  final String courseId;
  final DateTime date;
  final DateTime startTime;
  final DateTime endTime;
  final String desc;

  ScheduleModel(
      {required this.docId,
      required this.title,
      required this.courseId,
      required this.date,
      required this.startTime,
      required this.endTime,
      required this.desc});

  static List<ScheduleModel> toSchList(
          QuerySnapshot<Map<String, dynamic>> event) =>
      event.docs
          .map(
            (e) => ScheduleModel(
              docId: e.id,
              title: e['title'],
              courseId: e['courseId'],
              date: e['date'].toDate(),
              startTime: e['startTime'].toDate(),
              endTime: e['endTime'].toDate(),
              desc: e['desc'],
            ),
          )
          .toList();
}
