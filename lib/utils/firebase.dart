import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';

class FBAuth {
  static final auth = FirebaseAuth.instance;
}

class FBFireStore {
  static final fs = FirebaseFirestore.instance;
  static final users = fs.collection('users');
  static final sets = fs.collection('sets');
  static final data = sets.doc('data');
  static final courses = fs.collection('courses');
  static final orders = fs.collection('orders');
  static final userCourses = fs.collection('userCourses');
  static final certis = fs.collection('certis');
  static final schedules = fs.collection('schedules');
  static final scheduled = fs.collection('scheduled');
  static final otherCerti = fs.collection('otherCerti');
  static final eBranches = fs.collection('eBranches');
  // static final etrCourses = fs.collection('etrCourses');
}

class FBFunctions {
  static final ff = FirebaseFunctions.instance;
}

class FBStorage {
  static final fb = FirebaseStorage.instance;
  static final courseFiles = fb.ref().child('courseFiles');
  static final otherCertis = fb.ref().child('otherCertis');
}
