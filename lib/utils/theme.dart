import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

const appColorOne = Color(0xff009db8);
const appColorTwo = Color(0xff006837);
final appTitleTextStyle = GoogleFonts.playfairDisplay();
final appTextStyleOne = GoogleFonts.montserrat();
final appTextStyleTwo = GoogleFonts.ubuntu();
final appTextThemeOne = GoogleFonts.robotoTextTheme();
final cuperPlaceHolderText =
    appTextStyleOne.copyWith(color: Colors.grey.shade600, fontSize: 15);
final cuperSearchText = appTextStyleOne.copyWith();

final themeData = ThemeData(
    useMaterial3: true,
    colorSchemeSeed: appColorOne,
    textTheme: appTextThemeOne,
    pageTransitionsTheme: const PageTransitionsTheme(builders: {
      TargetPlatform.android: CupertinoPageTransitionsBuilder(),
      TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
    }));
