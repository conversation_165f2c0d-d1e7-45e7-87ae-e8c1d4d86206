import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import '../../models/course_model.dart';

class ExpandableChapters extends StatefulWidget {
  const ExpandableChapters({super.key, required this.chapters});
  final List<Chapters> chapters;
  @override
  State<ExpandableChapters> createState() => _ExpandableChaptersState();
}

class _ExpandableChaptersState extends State<ExpandableChapters> {
  late List<bool> _isOpen;
  @override
  void initState() {
    super.initState();
    _isOpen = List.generate(widget.chapters.length, (index) => false);
  }

  @override
  Widget build(BuildContext context) {
    return ExpansionPanelList(
      expansionCallback: (panelIndex, isExpanded) =>
          setState(() => _isOpen[panelIndex] = !isExpanded),
      children: List.generate(
          widget.chapters.length,
          (index) => ExpansionPanel(
              canTapOnHeader: true,
              isExpanded: _isOpen[index],
              headerBuilder: (context, isExpanded) {
                return Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(widget.chapters[index].name),
                      const SizedBox(width: 12),
                      Row(
                        children: [
                          const Icon(CupertinoIcons.timer, size: 18),
                          const SizedBox(width: 2),
                          Text.rich(
                            TextSpan(
                              text: '${widget.chapters[index].duration.hours} ',
                              style: TextStyle(color: Colors.grey.shade900),
                              children: [
                                TextSpan(
                                  text: "Hours ",
                                  style: TextStyle(color: Colors.grey.shade700),
                                ),
                                TextSpan(
                                  text:
                                      '${widget.chapters[index].duration.minutes} ',
                                  style: TextStyle(color: Colors.grey.shade900),
                                ),
                                TextSpan(
                                  text: "Minutes",
                                  style: TextStyle(color: Colors.grey.shade700),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                );
              },
              body: Container(
                color: Colors.grey.shade100,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ...List.generate(
                        widget.chapters[index].modules.length,
                        (idx) => Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 20, vertical: 12),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                      widget.chapters[index].modules[idx].name),
                                  const SizedBox(width: 12),
                                  Row(
                                    children: [
                                      const Icon(CupertinoIcons.timer,
                                          size: 18),
                                      const SizedBox(width: 2),
                                      Text.rich(
                                        TextSpan(
                                          text:
                                              '${widget.chapters[index].modules[idx].duration.hours} ',
                                          style: TextStyle(
                                              color: Colors.grey.shade900),
                                          children: [
                                            TextSpan(
                                              text: "Hours ",
                                              style: TextStyle(
                                                  color: Colors.grey.shade700),
                                            ),
                                            TextSpan(
                                              text:
                                                  '${widget.chapters[index].modules[idx].duration.minutes.toString().padRight(2, "0")} ',
                                              style: TextStyle(
                                                  color: Colors.grey.shade900),
                                            ),
                                            TextSpan(
                                              text: "Minutes",
                                              style: TextStyle(
                                                  color: Colors.grey.shade700),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            )),
                    ...List.generate(
                        widget.chapters[index].assignments.length,
                        (idx) => Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 20, vertical: 12),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(widget
                                      .chapters[index].assignments[idx].name),
                                  const SizedBox(width: 12),
                                  Row(
                                    children: [
                                      const Icon(CupertinoIcons.timer,
                                          size: 18),
                                      const SizedBox(width: 2),
                                      Text.rich(
                                        TextSpan(
                                          text:
                                              '${widget.chapters[index].assignments[idx].duration.hours} ',
                                          style: TextStyle(
                                              color: Colors.grey.shade900),
                                          children: [
                                            TextSpan(
                                              text: "Hours ",
                                              style: TextStyle(
                                                  color: Colors.grey.shade700),
                                            ),
                                            TextSpan(
                                              text:
                                                  '${widget.chapters[index].assignments[idx].duration.minutes.toString().padRight(2, "0")} ',
                                              style: TextStyle(
                                                  color: Colors.grey.shade900),
                                            ),
                                            TextSpan(
                                              text: "Minutes",
                                              style: TextStyle(
                                                  color: Colors.grey.shade700),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            )),
                  ],
                ),
              ))),
    );
  }
}
