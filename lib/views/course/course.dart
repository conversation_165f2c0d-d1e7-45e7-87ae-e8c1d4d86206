import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wellfed/controllers/home_ctrl.dart';
import 'package:wellfed/models/course_model.dart';
import 'package:wellfed/utils/firebase.dart';
import 'package:wellfed/utils/loaders.dart';
import 'package:wellfed/utils/methods.dart';
import 'package:wellfed/utils/responsive.dart';
import 'package:wellfed/utils/theme.dart';
import 'package:wellfed/views/course/buy_box.dart';
import '../../models/cart_model.dart';

class CourseDetailsPage extends StatelessWidget {
  const CourseDetailsPage({super.key, required this.courseDocId});

  final String? courseDocId;

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<CourseModel>(
      stream: FBFireStore.courses
          .doc(courseDocId)
          .snapshots()
          .map((event) => CourseModel.fromJson(event.id, event.data()!)),
      builder: (BuildContext context, snapshot) {
        if (snapshot.hasError) {
          debugPrint(snapshot.error.toString());
          return const Center(
              child: Text("Course Not Found",
                  style: TextStyle(fontWeight: FontWeight.bold)));
        }
        if (snapshot.hasData) {
          return snapshot.data == null
              ? const Center(
                  child: Text("Course Not Found",
                      style: TextStyle(fontWeight: FontWeight.bold)))
              : ResponsiveWid(
                  mobile: CourseDataWids(small: true, course: snapshot.data!),
                  tablet: CourseDataWids(small: true, course: snapshot.data!),
                  desktop: CourseDataWids(small: false, course: snapshot.data!),
                );
        }
        return Center(
            child: loaderWave(color: Theme.of(context).primaryColor, size: 30));
      },
    );
  }
}

class CourseDataWids extends StatelessWidget {
  const CourseDataWids({super.key, required this.small, required this.course});
  final bool small;
  final CourseModel course;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final bool isNarrow = constraints.maxWidth < 600;
        final EdgeInsets padding = small
            ? const EdgeInsets.all(20)
            : EdgeInsets.symmetric(
                horizontal: constraints.maxWidth * 0.1, vertical: 20);

        final titleStyle = appTitleTextStyle.copyWith(
          fontWeight: FontWeight.bold,
          fontSize: isNarrow ? 24 : 30,
          overflow: TextOverflow.ellipsis,
        );

        final subtitleStyle = appTextStyleTwo.copyWith(
          fontSize: isNarrow ? 16 : 20,
          height: 1.3,
        );

        final labelTextStyle = TextStyle(
          fontSize: isNarrow ? 14 : 16,
          color: Colors.grey.shade700,
          height: 1.3,
        );

        final valueTextStyle = TextStyle(
          fontSize: isNarrow ? 14 : 16,
          color: Colors.grey.shade900,
          fontWeight: FontWeight.w600,
          height: 1.3,
        );

        Widget buildInfoRow(IconData icon, List<InlineSpan> texts) {
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Icon(icon,
                    size: isNarrow ? 16 : 18, color: Colors.grey.shade600),
                const SizedBox(width: 6),
                Flexible(
                  child: Text.rich(
                    TextSpan(children: texts),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              ],
            ),
          );
        }

        int totalLectures = course.chapters.isNotEmpty
            ? course.chapters
                .map((e) => e.modules.length)
                .reduce((a, b) => a + b)
            : 0;

        Widget mainContent = Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(course.title, style: titleStyle),
            const SizedBox(height: 12),
            // Text(course.desc, style: subtitleStyle),
            // const SizedBox(height: 6),
            Text('By ${course.author}', style: labelTextStyle),
            const SizedBox(height: 8),
            Text.rich(
              TextSpan(
                text: "Last Updated on ",
                style: TextStyle(
                    color: Colors.green.shade700,
                    fontSize: labelTextStyle.fontSize),
                children: [
                  TextSpan(
                    text: course.updatedOn.goodDate(),
                    style: TextStyle(
                      color: Colors.green.shade800,
                      fontWeight: FontWeight.bold,
                      fontSize: labelTextStyle.fontSize,
                    ),
                  ),
                ],
              ),
            ),
            buildInfoRow(
              CupertinoIcons.timer,
              [
                TextSpan(
                    text: '${course.duration.hours} ', style: valueTextStyle),
                TextSpan(text: "Hours ", style: labelTextStyle),
                TextSpan(
                    text: '${course.duration.minutes} ', style: valueTextStyle),
                TextSpan(text: "Minutes", style: labelTextStyle),
              ],
            ),
            buildInfoRow(
              CupertinoIcons.play_rectangle,
              [
                TextSpan(
                    text: '${course.chapters.length} ', style: valueTextStyle),
                TextSpan(text: 'Chapters ', style: labelTextStyle),
                TextSpan(text: '$totalLectures ', style: valueTextStyle),
                TextSpan(text: 'Lectures', style: labelTextStyle),
              ],
            ),
            Padding(
              padding: const EdgeInsets.only(top: 6),
              child: Text.rich(
                TextSpan(
                  text: course.examType,
                  style: TextStyle(
                    color: Theme.of(context).primaryColor,
                    fontSize: labelTextStyle.fontSize,
                  ),
                  children: const [
                    TextSpan(
                      text: " Exam",
                      style: TextStyle(color: Colors.black),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 12),
            GetBuilder<HomeController>(
              init: Get.find<HomeController>(),
              builder: (_) {
                bool showBulkBox = _.isEnterprise && course.bulkMinQty != 1;
                bool isInCart = _.cartList
                    .contains(CartCourseModel(courseId: course.docId));
                return Row(
                  children: [
                    if (showBulkBox)
                      Container(
                        margin: const EdgeInsets.only(right: 10),
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor.withOpacity(.1),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Container(
                              padding: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: Text(
                                '\$${course.bulkPrice}',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                            Text(
                              "For ${course.bulkMinQty - 1}+",
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).primaryColor,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                    Expanded(
                      child: MaterialButton(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        color: const Color.fromARGB(255, 233, 246, 249),
                        hoverColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          side: BorderSide(color: appColorOne.withOpacity(.3)),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        onPressed: () {
                          _.addToCart(
                              context, CartCourseModel(courseId: course.docId));
                        },
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const SizedBox(width: 20),
                            Icon(
                              isInCart
                                  ? CupertinoIcons.bag
                                  : CupertinoIcons.bag_badge_plus,
                              color: appColorOne.withOpacity(.8),
                            ),
                            const SizedBox(width: 10),
                            Text(
                              isInCart ? "Go to Cart" : "Add to Cart",
                              style: const TextStyle(
                                  color: appColorOne, fontSize: 16),
                            ),
                            const SizedBox(width: 20),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: appColorOne.withOpacity(.08),
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Text(
                                    '\$${course.discountPrice}',
                                    style: TextStyle(
                                      color: Colors.grey.shade700,
                                      fontSize: 14,
                                      decoration: TextDecoration.lineThrough,
                                    ),
                                  ),
                                  Text(
                                    _.isEnterprise && course.bulkMinQty == 1
                                        ? '\$${course.bulkPrice}'
                                        : '\$${course.price}',
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
            const SizedBox(height: 24),
            Text(
              "Note: This course must be successfully finished within ${course.days} days from the date of ${isLoggedIn() ? (Get.find<HomeController>().isEnterprise ? "assigning" : "purchase") : "assigning/purchase"}.",
              style: const TextStyle(
                fontStyle: FontStyle.italic,
                fontSize: 14,
              ),
            ),
          ],
        );

        Widget overviewSection = Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Overview",
              style: TextStyle(
                fontSize: isNarrow ? 18 : 22,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade800,
              ),
            ),
            const Divider(),
            Text(
              course.overview,
              textAlign: TextAlign.justify,
              style: appTextStyleOne.copyWith(fontSize: isNarrow ? 14 : 15),
            ),
            const SizedBox(height: 40),
          ],
        );

        return SingleChildScrollView(
          padding: padding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              isNarrow
                  ? Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        BuyBox(course: course, large: !small),
                        const SizedBox(height: 20),
                        mainContent,
                      ],
                    )
                  : Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(flex: 3, child: mainContent),
                        const SizedBox(width: 20),
                        Expanded(child: BuyBox(course: course, large: !small)),
                      ],
                    ),
              const SizedBox(height: 20),
              overviewSection,
            ],
          ),
        );
      },
    );
  }
}
