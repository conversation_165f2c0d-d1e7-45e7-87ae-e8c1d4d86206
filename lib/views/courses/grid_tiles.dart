import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:wellfed/utils/methods.dart';
import 'package:wellfed/utils/router.dart';
import 'package:wellfed/utils/theme.dart';
import '../../controllers/home_ctrl.dart';
import '../../models/cart_model.dart';
import '../../models/course_model.dart';
import '../../utils/other.dart';

class CourseGridTile extends StatelessWidget {
  const CourseGridTile({
    super.key,
    required this.course,
    required this.isEnterprise,
  });

  final CourseModel course;
  final bool isEnterprise;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        context.go('${Routes.course}/${course.docId}');
      },
      child: Container(
        decoration:
            BoxDecoration(border: Border.all(color: Colors.grey.shade300)),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
                flex: 2,
                child: LimitedBox(
                    maxHeight: 250,
                    child: ImageBox(imageUrl: course.imageUrl))),
            Expanded(
              flex: 4,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      course.title,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: const TextStyle(
                          fontWeight: FontWeight.bold, fontSize: 24),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 2.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            course.desc,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: const TextStyle(),
                          ),
                          Text(
                            'By ${course.author}',
                            style: TextStyle(color: Colors.grey.shade700),
                          ),
                          Text.rich(
                            TextSpan(
                              text: "Last Updated on ",
                              style: TextStyle(color: Colors.green.shade700),
                              children: [
                                TextSpan(
                                  text: course.updatedOn.goodDate(),
                                  style: TextStyle(
                                      color: Colors.green.shade800,
                                      fontWeight: FontWeight.bold),
                                ),
                              ],
                            ),
                          ),
                          Row(
                            children: [
                              const Icon(CupertinoIcons.timer, size: 18),
                              const SizedBox(width: 2),
                              Text.rich(
                                TextSpan(
                                  text: '${course.duration.hours} ',
                                  style: TextStyle(color: Colors.grey.shade900),
                                  children: [
                                    TextSpan(
                                      text: "Hours ",
                                      style: TextStyle(
                                          color: Colors.grey.shade700),
                                    ),
                                    TextSpan(
                                      text: '${course.duration.minutes} ',
                                      style: TextStyle(
                                          color: Colors.grey.shade900),
                                    ),
                                    TextSpan(
                                      text: "Minutes",
                                      style: TextStyle(
                                          color: Colors.grey.shade700),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              const Icon(CupertinoIcons.play_rectangle,
                                  size: 18),
                              const SizedBox(width: 2),
                              Text.rich(
                                TextSpan(
                                  text: '${course.chapters.length} ',
                                  style: TextStyle(color: Colors.grey.shade900),
                                  children: [
                                    TextSpan(
                                      text: "Chapters ",
                                      style: TextStyle(
                                          color: Colors.grey.shade700),
                                    ),
                                    TextSpan(
                                      text:
                                          '${course.chapters.map((e) => e.modules.length).reduce((value, element) => value + element)} ',
                                      style: TextStyle(
                                          color: Colors.grey.shade900),
                                    ),
                                    TextSpan(
                                      text: "Lectures",
                                      style: TextStyle(
                                          color: Colors.grey.shade700),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          Text.rich(
                            TextSpan(
                              text: course.examType,
                              style: TextStyle(
                                  color: Theme.of(context).primaryColor),
                              children: const [
                                TextSpan(
                                  text: " Exam",
                                  style: TextStyle(color: Colors.black),
                                ),
                              ],
                            ),
                          ),
                          // const Spacer(),
                          const SizedBox(height: 18),
                          GetBuilder<HomeController>(
                            init: Get.find<HomeController>(),
                            builder: (_) {
                              return OutlinedButton.icon(
                                style: OutlinedButton.styleFrom(
                                    backgroundColor:
                                        appColorOne.withOpacity(.1),
                                    side: BorderSide(
                                        color: appColorOne.withOpacity(.4)),
                                    shape: RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(4))),
                                onPressed: () {
                                  _.addToCart(context,
                                      CartCourseModel(courseId: course.docId));
                                },
                                icon: _.cartList.contains(
                                        CartCourseModel(courseId: course.docId))
                                    ? const Icon(CupertinoIcons.cart)
                                    : const Icon(
                                        CupertinoIcons.cart_badge_plus),
                                label: Text(
                                    '${_.cartList.contains(CartCourseModel(courseId: course.docId)) ? "Go" : "Add"} to Cart'),
                              );
                            },
                          ),
                          const SizedBox(height: 2)
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '\$${course.discountPrice}',
                    style: TextStyle(
                        color: Colors.grey.shade700,
                        decoration: TextDecoration.lineThrough),
                  ),
                  Text(
                    isEnterprise && course.bulkMinQty == 1
                        ? '\$${course.bulkPrice}'
                        : '\$${course.price}',
                    style: const TextStyle(
                        fontWeight: FontWeight.bold, fontSize: 18),
                  ),
                  if (isEnterprise && course.bulkMinQty != 1)
                    Container(
                      margin: const EdgeInsets.only(top: 6),
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor.withOpacity(.1),
                          borderRadius: BorderRadius.circular(8)),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Container(
                            padding: const EdgeInsets.all(2),
                            decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(4)),
                            child: Text(
                              '\$${course.bulkPrice}',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                              ),
                            ),
                          ),
                          Text(
                            "For ${course.bulkMinQty - 1}+",
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).primaryColor,
                            ),
                          )
                        ],
                      ),
                    ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}

class ImageBox extends StatelessWidget {
  const ImageBox({
    super.key,
    required this.imageUrl,
  });

  final String imageUrl;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(12),
      decoration:
          BoxDecoration(border: Border.all(color: Colors.grey.shade300)),
      child: SizedBox.expand(
        child: FadeInImage.memoryNetwork(
            placeholderErrorBuilder: (context, error, stackTrace) =>
                Image.memory(placeholderGrad),
            placeholder: placeholderGrad,
            fit: BoxFit.cover,
            image: imageUrl),
      ),
    );
  }
}

class CourseGridTileSmall extends StatelessWidget {
  const CourseGridTileSmall({
    super.key,
    required this.course,
    required this.isEnterprise,
  });

  final CourseModel course;
  final bool isEnterprise;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        context.go('${Routes.course}/${course.docId}');
      },
      child: Container(
        decoration:
            BoxDecoration(border: Border.all(color: Colors.grey.shade300)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AspectRatio(
                aspectRatio: 16 / 9,
                child: ImageBox(imageUrl: course.imageUrl)),
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    course.title,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: const TextStyle(
                        fontWeight: FontWeight.bold, fontSize: 24),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 2.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          course.desc,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: const TextStyle(),
                        ),
                        Text(
                          'By ${course.author}',
                          style: TextStyle(color: Colors.grey.shade700),
                        ),
                        Text.rich(
                          TextSpan(
                            text: "Last Updated on ",
                            style: TextStyle(color: Colors.green.shade700),
                            children: [
                              TextSpan(
                                text: course.updatedOn.goodDate(),
                                style: TextStyle(
                                    color: Colors.green.shade800,
                                    fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                        ),
                        Row(
                          children: [
                            const Icon(CupertinoIcons.timer, size: 18),
                            const SizedBox(width: 2),
                            Text.rich(
                              TextSpan(
                                text: '${course.duration.hours} ',
                                style: TextStyle(color: Colors.grey.shade900),
                                children: [
                                  TextSpan(
                                    text: "Hours ",
                                    style:
                                        TextStyle(color: Colors.grey.shade700),
                                  ),
                                  TextSpan(
                                    text: '${course.duration.minutes} ',
                                    style:
                                        TextStyle(color: Colors.grey.shade900),
                                  ),
                                  TextSpan(
                                    text: "Minutes",
                                    style:
                                        TextStyle(color: Colors.grey.shade700),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            const Icon(CupertinoIcons.play_rectangle, size: 18),
                            const SizedBox(width: 2),
                            Text.rich(
                              TextSpan(
                                text: '${course.chapters.length} ',
                                style: TextStyle(color: Colors.grey.shade900),
                                children: [
                                  TextSpan(
                                    text: "Chapters ",
                                    style:
                                        TextStyle(color: Colors.grey.shade700),
                                  ),
                                  TextSpan(
                                    text:
                                        '${course.chapters.map((e) => e.modules.length).reduce((value, element) => value + element)} ',
                                    style:
                                        TextStyle(color: Colors.grey.shade900),
                                  ),
                                  TextSpan(
                                    text: "Lectures",
                                    style:
                                        TextStyle(color: Colors.grey.shade700),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        Text.rich(
                          TextSpan(
                            text: course.examType,
                            style: TextStyle(
                                color: Theme.of(context).primaryColor),
                            children: const [
                              TextSpan(
                                text: " Exam",
                                style: TextStyle(color: Colors.black),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 20),
                        GetBuilder<HomeController>(
                          init: Get.find<HomeController>(),
                          builder: (_) {
                            return Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                if (isEnterprise && course.bulkMinQty != 1)
                                  Container(
                                    margin: const EdgeInsets.only(right: 6),
                                    padding: const EdgeInsets.all(4),
                                    decoration: BoxDecoration(
                                        color: Theme.of(context)
                                            .primaryColor
                                            .withOpacity(.1),
                                        borderRadius: BorderRadius.circular(8)),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.end,
                                      children: [
                                        Container(
                                          padding: const EdgeInsets.all(2),
                                          decoration: BoxDecoration(
                                              color: Colors.white,
                                              borderRadius:
                                                  BorderRadius.circular(4)),
                                          child: Text(
                                            '\$${course.bulkPrice}',
                                            style: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                              color: Colors.black,
                                            ),
                                          ),
                                        ),
                                        Text(
                                          "For ${course.bulkMinQty - 1}+",
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color:
                                                Theme.of(context).primaryColor,
                                          ),
                                        )
                                      ],
                                    ),
                                  ),
                                Expanded(
                                  child: MaterialButton(
                                    padding: const EdgeInsets.all(14),
                                    color: const Color.fromARGB(
                                        255, 233, 246, 249),
                                    hoverColor: Colors.white,
                                    // color: const Color.fromARGB(255, 32, 32, 32),
                                    shape: RoundedRectangleBorder(
                                        side: BorderSide(
                                            color: appColorOne.withOpacity(.3)),
                                        borderRadius:
                                            BorderRadius.circular(12)),
                                    onPressed: () {
                                      _.addToCart(
                                          context,
                                          CartCourseModel(
                                              courseId: course.docId));
                                    },
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 0.0),
                                      child: Row(
                                        // mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Expanded(
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                _.cartList.contains(
                                                        CartCourseModel(
                                                            courseId:
                                                                course.docId))
                                                    ? Icon(CupertinoIcons.bag,
                                                        color: appColorOne
                                                            .withOpacity(.8))
                                                    : Icon(
                                                        CupertinoIcons
                                                            .bag_badge_plus,
                                                        color: appColorOne
                                                            .withOpacity(.8)),
                                                const SizedBox(width: 8),
                                                Text(
                                                  '${_.cartList.contains(CartCourseModel(courseId: course.docId)) ? "Go" : "Add"} to Cart',
                                                  style: const TextStyle(
                                                      color: appColorOne),
                                                ),
                                              ],
                                            ),
                                          ),
                                          const SizedBox(width: 18),
                                          Container(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 6, vertical: 2),
                                            decoration: BoxDecoration(
                                                color: appColorOne
                                                    .withOpacity(.08),
                                                borderRadius:
                                                    BorderRadius.circular(8)),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.end,
                                              children: [
                                                Text(
                                                  '\$${course.discountPrice}',
                                                  style: TextStyle(
                                                      color:
                                                          Colors.grey.shade700,
                                                      fontSize: 12,
                                                      decoration: TextDecoration
                                                          .lineThrough),
                                                ),
                                                Text(
                                                  isEnterprise &&
                                                          course.bulkMinQty == 1
                                                      ? '\$${course.bulkPrice}'
                                                      : '\$${course.price}',
                                                  style: const TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                    // color: Colors.white,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            );
                          },
                        ),
                        const SizedBox(height: 2)
                        /*  const Spacer(),
                        ElevatedButton.icon(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: appColorOne,
                            elevation: 0,
                            shape: ContinuousRectangleBorder(
                                borderRadius: BorderRadius.circular(4)),
                          ),
                          onPressed: () {},
                          label: const Text(
                            "Add to Cart",
                            style: TextStyle(color: Colors.white),
                          ),
                          icon: const Icon(CupertinoIcons.cart_badge_plus,
                              color: Colors.white),
                        ),
                        const SizedBox(height: 2), */
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
