import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wellfed/controllers/home_ctrl.dart';
import 'package:wellfed/utils/consts.dart';
import 'package:wellfed/utils/responsive.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:wellfed/utils/theme.dart';
import 'package:wellfed/views/webview/copyright.dart';
import 'package:wellfed/views/webview/footer.dart';

import 'grid_tiles.dart';

class Courses extends StatefulWidget {
  const Courses({super.key});

  @override
  State<Courses> createState() => _CoursesState();
}

class _CoursesState extends State<Courses> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return const ResponsiveWid(
      mobile: CourseWids(small: true),
      tablet: CourseWids(small: false),
      desktop: CourseWids(small: false),
    );
  }
}

class CourseWids extends StatelessWidget {
  const CourseWids({
    super.key,
    required this.small,
  });

  final bool small;

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    return GetBuilder<HomeController>(
      init: Get.find<HomeController>(),
      builder: (_) {
        final filteredList = _.courseList
            .where((element) =>
                element.title
                    .toLowerCase()
                    .contains(_.searchCtrl.text.toLowerCase()) &&
                (_.selectedCat == "All"
                    ? true
                    : element.tags.contains(_.selectedCat)))
            .toList();
        return SingleChildScrollView(
          child: Column(
            children: [
              Padding(
                padding: small
                    ? const EdgeInsets.all(20)
                    : EdgeInsets.symmetric(
                        horizontal: size.width * .12, vertical: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const _HeaderOne(),
                    const _HeaderTwo(),
                    const SizedBox(height: 24),
                    Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _.searchCtrl,
                            decoration: InputDecoration(
                              contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 20, vertical: 0),
                              labelText: "Search",
                              hintText: "Search for courses",
                              alignLabelWithHint: true,
                              prefixIcon: const Icon(CupertinoIcons.search),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(45),
                              ),
                            ),
                            onChanged: (value) => _.update(),
                          ),
                        ),
                        const SizedBox(width: 20),
                        SizedBox(
                          width: 140,
                          child: DropdownButtonFormField(
                            borderRadius: BorderRadius.circular(4),
                            value: _.selectedCat,
                            items: List.generate(
                              categoryTypes.length,
                              (index) => DropdownMenuItem(
                                value: categoryTypes[index],
                                child: Text(categoryTypes[index]),
                              ),
                            ),
                            decoration: InputDecoration(
                              border: InputBorder.none,
                              fillColor: Colors.grey.shade100,
                              filled: true,
                            ),
                            onChanged: (value) {
                              _.selectedCat = value!;
                              _.update();
                            },
                          ),
                        )
                      ],
                    ),
                    const SizedBox(height: 20),
                    filteredList.isEmpty
                        ? Center(
                            child: Padding(
                            padding: const EdgeInsets.all(20.0),
                            child: Text(_.courseLoaded
                                ? "No Course Found!"
                                : "Loading"),
                          ))
                        : StaggeredGrid.count(
                            mainAxisSpacing: 20,
                            crossAxisSpacing: 20,
                            crossAxisCount: 1,
                            children: List.generate(
                              _.courseList.length,
                              (index) => small
                                  ? CourseGridTileSmall(
                                      course: filteredList[index],
                                      isEnterprise: _.isEnterprise)
                                  : CourseGridTile(
                                      course: filteredList[index],
                                      isEnterprise: _.isEnterprise),
                            ),
                          ),
                  ],
                ),
              ),
              const SizedBox(height: 40),
              FooterBox(size: size),
              CopyRightBar(size: size),
            ],
          ),
        );
      },
    );
  }
}

class _HeaderOne extends StatelessWidget {
  const _HeaderOne();

  @override
  Widget build(BuildContext context) {
    return Text(
      "Food Safety Courses",
      style:
          appTitleTextStyle.copyWith(fontSize: 32, fontWeight: FontWeight.bold),
    );
  }
}

class _HeaderTwo extends StatelessWidget {
  const _HeaderTwo();

  @override
  Widget build(BuildContext context) {
    return const Text(
      "Courses to get you started",
      style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
    );
  }
}
