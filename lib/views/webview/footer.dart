import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:wellfed/views/webview/webview.dart';
import '../../utils/consts.dart';
import '../../utils/router.dart';
import '../../utils/theme.dart';

class FooterBox extends StatelessWidget {
  const FooterBox({super.key, required this.size});
  final Size size;

  @override
  Widget build(BuildContext context) {
    bool large = size.width > breakPointLarge;
    bool small = size.width < breakPointMid;
    return Container(
      color: Theme.of(context).primaryColor.withOpacity(.1),
      padding: !large
          ? EdgeInsets.symmetric(
              horizontal: small ? 28 : size.width * .1,
              vertical: small ? 34 : size.width * .06)
          : homeLargeInsects(size, horizontal: size.width * .02),
      child: !small
          ? Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  flex: 2,
                  child: _columnOne(),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _c2textTitle(),
                      const SizedBox(height: 20),
                      _c2textOne(context),
                      const SizedBox(height: 20),
                      _c2textTwo(context),
                      const SizedBox(height: 20),
                      _c2textThree(context),
                      const SizedBox(height: 20),
                      _c2textFour(context),
                      const SizedBox(height: 20),
                      _c2textFive(context),
                      const SizedBox(height: 20),
                      _c2textSix(),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _c3textTitle(),
                      const SizedBox(height: 20),
                      _c3textOne(),
                      const SizedBox(height: 20),
                      _c3textTwo(),
                      const SizedBox(height: 20),
                      _c3textThree(),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _c4textTitle(),
                      const SizedBox(height: 20),
                      _c4textOne(),
                      const SizedBox(height: 20),
                      _emailField(),
                      const SizedBox(height: 20),
                      _c4textTitle2(),
                      const SizedBox(height: 20),
                      CardImage(),
                    ],
                  ),
                ),
              ],
            )
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _columnOne(),
                const SizedBox(height: 20),
                Theme(
                  data: Theme.of(context)
                      .copyWith(hoverColor: Colors.transparent),
                  child: ExpansionTile(
                    tilePadding: EdgeInsets.zero,
                    title: _c2textTitle(),
                    shape: const RoundedRectangleBorder(side: BorderSide.none),
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20),
                        child: Column(
                          children: [
                            const SizedBox(height: 20),
                            _c2textOne(context),
                            const SizedBox(height: 20),
                            _c2textTwo(context),
                            const SizedBox(height: 20),
                            _c2textThree(context),
                            const SizedBox(height: 20),
                            _c2textFour(context),
                            const SizedBox(height: 20),
                            _c2textFive(context),
                            const SizedBox(height: 20),
                            _c2textSix(),
                            const SizedBox(height: 20),
                          ],
                        ),
                      ),
                      const Divider(
                        height: 0,
                        color: Colors.black54,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
                Theme(
                  data: Theme.of(context)
                      .copyWith(hoverColor: Colors.transparent),
                  child: ExpansionTile(
                    tilePadding: EdgeInsets.zero,
                    shape: const RoundedRectangleBorder(side: BorderSide.none),
                    title: _c3textTitle(),
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20),
                        child: Column(
                          children: [
                            const SizedBox(height: 20),
                            _c3textOne(),
                            const SizedBox(height: 20),
                            _c3textTwo(),
                            const SizedBox(height: 20),
                            _c3textThree(),
                            const SizedBox(height: 20),
                          ],
                        ),
                      ),
                      const Divider(
                        height: 0,
                        color: Colors.black54,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
                _c4textTitle(),
                const SizedBox(height: 20),
                _c4textOne(),
                const SizedBox(height: 20),
                _emailField(),
                const SizedBox(height: 20),
                _c4textTitle2(),
                const SizedBox(height: 20),
                CardImage(),
              ],
            ),
    );
  }

  Text _c4textTitle2() {
    return const Text(
      "We Accept",
      style: TextStyle(
        letterSpacing: 1.2,
        fontSize: 18,
        fontFamily: 'Roboto',
        fontWeight: FontWeight.w900,
      ),
    );
  }

  Row _emailField() {
    return Row(
      children: [
        Expanded(
            child: TextField(
          decoration: InputDecoration(
            fillColor: Colors.white70,
            filled: true,
            hintText: "<EMAIL>",
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(2),
              borderSide: const BorderSide(color: Colors.black12),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(2),
              borderSide: const BorderSide(color: Colors.black38),
            ),
          ),
        )),
        IconButton.filled(
            style: IconButton.styleFrom(
                fixedSize: const Size(50, 55),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(2))),
            onPressed: () {},
            icon: const Icon(CupertinoIcons.paperplane))
      ],
    );
  }

  Text _c4textOne() {
    return const Text(
      "Subscribe to stay updated on latest courses & offers",
      style: TextStyle(
        letterSpacing: 1,
        fontSize: 16,
      ),
    );
  }

  Text _c4textTitle() {
    return const Text(
      "Newsletter",
      style: TextStyle(
        letterSpacing: 1.2,
        fontSize: 20,
        fontFamily: 'Roboto',
        fontWeight: FontWeight.w900,
      ),
    );
  }

  Text _c3textTitle() {
    return const Text(
      "Legal",
      style: TextStyle(
        letterSpacing: 1.2,
        fontSize: 20,
        fontFamily: 'Roboto',
        fontWeight: FontWeight.w900,
      ),
    );
  }

  Text _c2textTitle() {
    return const Text(
      "Site Map",
      style: TextStyle(
        letterSpacing: 1.2,
        fontSize: 20,
        fontFamily: 'Roboto',
        fontWeight: FontWeight.w900,
      ),
    );
  }

  FooterTextButton _c3textThree() =>
      FooterTextButton(text: "Privacy Policy", callback: () {});

  FooterTextButton _c3textTwo() =>
      FooterTextButton(text: "Terms & Conditions", callback: () {});

  FooterTextButton _c3textOne() =>
      FooterTextButton(text: "Refund Policy", callback: () {});

  FooterTextButton _c2textSix() =>
      FooterTextButton(text: "Contact", callback: () {});

  FooterTextButton _c2textFive(BuildContext context) {
    return FooterTextButton(
      text: "Our Process",
      callback: () {
        if (GoRouterState.of(context).uri.toString() != Routes.home) {
          context.go(Routes.home, extra: 'prc');
        }
        homeScroll("prc");
      },
    );
  }

  FooterTextButton _c2textFour(BuildContext context) {
    return FooterTextButton(
      text: "Exam Location",
      callback: () {
        if (GoRouterState.of(context).uri.toString() != Routes.home) {
          context.go(Routes.home, extra: 'loc');
        }
        homeScroll("loc");
      },
    );
  }

  FooterTextButton _c2textThree(BuildContext context) {
    return FooterTextButton(
      text: "Enterprise Bulk Order",
      callback: () {
        if (GoRouterState.of(context).uri.toString() != Routes.home) {
          context.go(Routes.home, extra: 'entr');
        }
        homeScroll("entr");
      },
    );
  }

  FooterTextButton _c2textTwo(BuildContext context) {
    return FooterTextButton(
      text: "Courses",
      callback: () => context.go(Routes.courses),
    );
  }

  FooterTextButton _c2textOne(BuildContext context) {
    return FooterTextButton(
      text: "Home",
      callback: () => context.go(Routes.home),
    );
  }

  Column _columnOne() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.only(right: 20.0),
          child: Text(
            "Lorem, ipsum dolor sit amet consectetur adipisicing elit. Praesentium nesciunt maxime ducimus repellendus voluptates",
            style: appTextStyleOne.copyWith(fontWeight: FontWeight.bold),
          ),
        ),
        const SizedBox(height: 24),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Icon(
              Icons.location_on_outlined,
              color: Colors.grey,
              size: 20,
            ),
            const SizedBox(width: 12),
            Text(
              "FSSMC # 00004221\nServsafe # 9049878\nCOCIN - 183",
              style: appTextStyleOne.copyWith(fontWeight: FontWeight.bold),
            )
          ],
        ),
        const SizedBox(height: 20),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Icon(
              Icons.phone_in_talk_outlined,
              color: Colors.grey,
              size: 20,
            ),
            const SizedBox(width: 12),
            Text(
              "+****************",
              style: appTextStyleOne.copyWith(fontWeight: FontWeight.bold),
            )
          ],
        ),
        const SizedBox(height: 20),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Icon(
              CupertinoIcons.mail,
              color: Colors.grey,
              size: 18,
            ),
            const SizedBox(width: 12),
            Text(
              "<EMAIL>",
              style: appTextStyleOne.copyWith(fontWeight: FontWeight.bold),
            )
          ],
        ),
        const SizedBox(height: 20),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            IconButton(
              onPressed: () {},
              icon: Image.asset(
                'assets/fb.png',
                height: 20,
                color: Colors.grey.shade700,
              ),
            ),
            IconButton(
              onPressed: () {},
              icon: Image.asset(
                'assets/in.png',
                height: 20,
                color: Colors.grey.shade700,
              ),
            ),
            IconButton(
              onPressed: () {},
              icon: Image.asset(
                'assets/ln.png',
                height: 20,
                color: Colors.grey.shade700,
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class CardImage extends StatelessWidget {
  const CardImage({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Image.asset(
          'assets/img.png',
          // height: 100,
          // color: Colors.grey.shade700,
        ),
      ],
    );
  }
}

class FooterTextButton extends StatefulWidget {
  const FooterTextButton({
    super.key,
    required this.text,
    required this.callback,
  });
  final String text;
  final Function callback;

  @override
  State<FooterTextButton> createState() => _FooterTextButtonState();
}

class _FooterTextButtonState extends State<FooterTextButton> {
  bool isHovered = false;
  @override
  Widget build(BuildContext context) {
    return InkWell(
      hoverColor: Colors.transparent,
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onTap: () => widget.callback(),
      onHover: (value) {
        setState(() => isHovered = value);
      },
      child: Row(
        children: [
          AnimatedSize(
            // : isHovered ? null : 0,
            duration: const Duration(milliseconds: 150),
            child: AnimatedOpacity(
              opacity: isHovered ? 1 : 0,
              duration: const Duration(milliseconds: 300),
              child: Text(
                isHovered ? " // " : "",
                style: _txtStyle(),
              ),
            ),
          ),
          Expanded(
            child: Text(
              widget.text,
              style: _txtStyle(),
            ),
          ),
        ],
      ),
    );
  }

  TextStyle _txtStyle() {
    return TextStyle(
      letterSpacing: 1,
      fontSize: 16,
      color: isHovered ? appColorTwo : null,
    );
  }
}
