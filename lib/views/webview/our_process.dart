import 'package:flutter/material.dart';
import '../../utils/consts.dart';
import '../../utils/theme.dart';
import 'webview.dart';

class OurProcess extends StatelessWidget {
  const OurProcess({super.key, required this.size});
  final Size size;

  @override
  Widget build(BuildContext context) {
    bool large = size.width > breakPointLarge;
    bool small = size.width < breakPointMid;
    return Padding(
      padding: !large
          ? EdgeInsets.symmetric(
              horizontal: small ? 28 : size.width * .1,
              vertical: small ? 34 : size.width * .06)
          : homeLargeInsects(size),
      child: Column(
        children: [
          !small
              ? Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      flex: 2,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _textOne(),
                          _textTwo(),
                          _textThree(),
                        ],
                      ),
                    ),
                    // const SizedBox(width: 20),
                    Expanded(
                        child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        _countText(),
                      ],
                    ))
                  ],
                )
              : Column(
                  children: [
                    _textOne(),
                    _textTwo(),
                    _textThree(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        _countText(),
                      ],
                    ),
                  ],
                ),
          if (!small) const SizedBox(height: 20),
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: IntrinsicHeight(
              child: !small
                  ? Row(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Expanded(
                          child: _stepOne(),
                        ),
                        VerticalDivider(color: Colors.grey.shade300),
                        Expanded(
                          child: _stepTwo(),
                        ),
                        VerticalDivider(color: Colors.grey.shade300),
                        Expanded(
                          child: _stepThree(),
                        ),
                        VerticalDivider(color: Colors.grey.shade300),
                        Expanded(
                          child: _stepFour(),
                        ),
                      ],
                    )
                  : Column(
                      children: [
                        _stepOne(),
                        const Divider(),
                        _stepTwo(),
                        const Divider(),
                        _stepThree(),
                        const Divider(),
                        _stepFour(),
                      ],
                    ),
            ),
          )
        ],
      ),
    );
  }

  StepBox _stepFour() {
    return const StepBox(
        number: "04",
        title: "Download your Certificate",
        desc:
            "Certainly! When you receive your certificate from us, you can rest assured that it will be conveniently accessible through our online portal for the entire duration of its validity.");
  }

  StepBox _stepThree() {
    return const StepBox(
        number: "03",
        title: "Schedule an In-Person Examination",
        desc:
            "Receive a 70% or higher to pass. If you fail, no need to worry, you will have a second chance to retake the test as well as an additional 30 day access to the virtual learning.");
  }

  StepBox _stepTwo() {
    return const StepBox(
        number: "02",
        title: "Enroll Online & Access Virtual Learning",
        desc:
            "Modules are about 15-20 minutes per segment, at the comfort of your own time. Upon completion an Hours of Training Certificate will be generated.");
  }

  StepBox _stepOne() {
    return const StepBox(
        number: "01",
        title: "Select & Buy a Course",
        desc:
            "Individual courses may be purchased, but to experience the complete and robust Employee Data Management system, consider buying 2 or more.");
  }

  Stack _countText() {
    return Stack(
      alignment: Alignment.centerLeft,
      children: [
        Text(
          "04",
          style: appTextStyleOne.copyWith(
            fontSize: 100,
            color: Colors.grey.shade300,
            fontWeight: FontWeight.w900,
            // fontFamily: 'Roboto',
          ),
        ),
        Row(
          children: [
            Container(
              height: 1,
              width: 30,
              color: Colors.black87,
            ),
            const SizedBox(width: 8),
            Text(
              "STEPS",
              style: appTextStyleOne.copyWith(
                fontSize: 12,
                letterSpacing: 1.8,
                fontWeight: FontWeight.bold,
                fontFamily: 'Roboto',
              ),
            ),
          ],
        ),
      ],
    );
  }

  Text _textThree() {
    return Text(
      "FOLLOW THESE EASY STEPS TO ENROLL FOR ONLINE TRAINING AND EXAMINATIONS.",
      textAlign: TextAlign.center,
      style: TextStyle(
        fontSize: 14,
        letterSpacing: 1.5,
        color: Colors.grey.shade800,
        fontWeight: FontWeight.bold,
        fontFamily: 'Roboto',
      ),
    );
  }

  Text _textTwo() {
    return Text.rich(
      textAlign: TextAlign.center,
      TextSpan(
          text: "Food Manager ",
          style: appTextStyleTwo.copyWith(
            fontSize: 34,
            color: appColorTwo,
            fontWeight: FontWeight.normal,
          ),
          children: [
            TextSpan(
              text: "course ",
              style: appTextStyleTwo.copyWith(
                fontSize: 34,
                color: Colors.black,
                fontWeight: FontWeight.bold,
                fontFamily: 'Roboto',
              ),
            ),
          ]),
    );
  }

  Text _textOne() {
    return Text.rich(
      TextSpan(
          text: "4 Steps to Get ",
          style: appTextStyleTwo.copyWith(
            fontSize: 34,
            height: 1,
            fontWeight: FontWeight.bold,
            fontFamily: 'Roboto',
          ),
          children: [
            TextSpan(
              text: "Certified with Your ",
              style: appTextStyleTwo.copyWith(
                  fontSize: 34,
                  height: 1,
                  color: appColorTwo,
                  fontWeight: FontWeight.normal),
            ),
          ]),
      textAlign: TextAlign.center,
    );
  }
}

class StepBox extends StatelessWidget {
  const StepBox(
      {super.key,
      required this.number,
      required this.title,
      required this.desc});
  final String number;
  final String title;
  final String desc;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: appColorTwo, width: 3.5),
            ),
            child: Text(
              number,
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
            ),
          ),
          const SizedBox(height: 20),
          Text(
            title,
            style: appTextStyleOne.copyWith(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              fontFamily: 'Roboto',
            ),
          ),
          const SizedBox(height: 12),
          Text(
            desc,
            style: appTextStyleOne.copyWith(
              // fontSize: 16,
              color: Colors.grey.shade700,
              fontWeight: FontWeight.bold,
              // fontFamily: 'Roboto',
            ),
          ),
        ],
      ),
    );
  }
}
