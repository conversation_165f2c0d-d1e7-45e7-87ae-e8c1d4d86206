import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:wellfed/controllers/home_ctrl.dart';
import 'package:wellfed/utils/consts.dart';
import 'package:wellfed/views/webview/webview.dart';
import '../../utils/router.dart';
import '../../utils/theme.dart';

class Header extends StatelessWidget {
  const Header({super.key, required this.size});
  final Size size;

  @override
  Widget build(BuildContext context) {
    bool large = size.width > breakPointLarge;
    bool small = size.width < breakPointMid;
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(colors: [
          Theme.of(context).primaryColor.withOpacity(.1),
          Colors.transparent
        ], stops: large ? const [.6, .6] : [.7, .7]),
      ),
      child: Padding(
        padding: !large
            ? EdgeInsets.symmetric(
                horizontal: small ? 28 : size.width * .1,
                vertical: small ? 34 : size.width * .06)
            : homeLargeInsects(size),
        child: !small
            ? Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _textOne(context),
                        const SizedBox(height: 20),
                        _textTwo(),
                        const SizedBox(height: 12),
                        _textThreeBox(),
                        const SizedBox(height: 30),
                        Wrap(
                          spacing: 40,
                          children: [
                            _pointOne(true),
                            _pointTwo(true),
                            _pointThree(true),
                          ],
                        ),
                        const SizedBox(height: 32),
                        Wrap(
                          runSpacing: 12,
                          children: [
                            _buttonOne(context),
                            const SizedBox(width: 20),
                            _buttonTwo(context)
                          ],
                        ),
                      ],
                    ),
                  ),
                  // const SizedBox(width: 20),
                  Expanded(
                      child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      _image(true),
                    ],
                  ))
                ],
              )
            : Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _textOne(context),
                  const SizedBox(height: 20),
                  _textTwo(),
                  const SizedBox(height: 12),
                  _textThreeBox(),
                  const SizedBox(height: 30),
                  // Wrap(
                  //   alignment: WrapAlignment.start,
                  //   spacing: 20,
                  //   runSpacing: 8,
                  //   children: [
                  _pointOne(false),
                  const SizedBox(height: 6),
                  _pointTwo(false),
                  const SizedBox(height: 6),
                  _pointThree(false),
                  //   ],
                  // ),
                  const SizedBox(height: 32),
                  Row(
                    children: [
                      _buttonOne(context),
                      const SizedBox(width: 20),
                      Flexible(child: _buttonTwo(context)),
                      const SizedBox(width: 8),
                    ],
                  ),
                  const SizedBox(height: 40),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      _image(false),
                    ],
                  ),
                ],
              ),
      ),
    );
  }

  Widget _image(bool inRow) {
    return LimitedBox(
      maxWidth: 480,
      maxHeight: 480,
      child: Image.asset(
        'assets/1.webp',
        height: inRow ? size.width * .308 : size.width * .75,
        width: inRow ? size.width * .308 : size.width * .75,
      ),
    );
  }

  ElevatedButton _buttonTwo(BuildContext context) {
    return ElevatedButton(
        style: ElevatedButton.styleFrom(
            elevation: 8,
            surfaceTintColor: Colors.white,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(4))),
        onPressed: () {
          context.go('${Routes.course}/$currentCourseDocId');
          // if (!isLoggedIn()) {
          //   context.go(Routes.login);
          //   return;
          // }
          // context.go(Routes.checkout,
          //     extra: CheckoutModel(checkoutCourses: [
          //       CartCourseModel(courseId: currentCourseDocId)
          //     ], enableEdit: true));
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 16),
          child: GetBuilder<HomeController>(
            init: Get.find<HomeController>(),
            builder: (_) {
              return Text(
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                "Enroll Now - \$${_.courseList.firstWhereOrNull((element) => element.docId == currentCourseDocId)?.price ?? ""}",
                style: const TextStyle(fontWeight: FontWeight.bold),
              );
            },
          ),
        ));
  }

  ElevatedButton _buttonOne(BuildContext context) {
    return ElevatedButton(
        style: ElevatedButton.styleFrom(
            elevation: 0,
            backgroundColor: Theme.of(context).primaryColor,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(4))),
        onPressed: () {
          homeScroll("ftr");
          // context.go('${Routes.course}/$currentCourseDocId');
        },
        child: const Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.0, vertical: 16),
          child: Text(
            "Learn More",
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ));
  }

  Row _pointThree(bool small) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        const Icon(
          CupertinoIcons.check_mark_circled_solid,
          color: Colors.green,
        ),
        const SizedBox(width: 6),
        Text(
          "ANSI${small ? '\n' : ' '}Certified",
          style: const TextStyle(
            fontWeight: FontWeight.w700,
            fontFamily: 'Montserrat',
          ),
        ),
      ],
    );
  }

  Row _pointTwo(bool small) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        const Icon(
          CupertinoIcons.check_mark_circled_solid,
          color: Colors.green,
        ),
        const SizedBox(width: 6),
        Text(
          "Easy Step-by-Step${small ? '\n' : ' '}Training",
          style: const TextStyle(
            fontWeight: FontWeight.w700,
            fontFamily: 'Montserrat',
          ),
        ),
      ],
    );
  }

  Row _pointOne(bool small) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        const Icon(
          CupertinoIcons.check_mark_circled_solid,
          color: Colors.green,
        ),
        const SizedBox(width: 6),
        Text(
          "National${small ? '\n' : ' '}Validation",
          style: const TextStyle(
            letterSpacing: 1.2,
            fontWeight: FontWeight.w700,
            fontFamily: 'Montserrat',
          ),
        ),
      ],
    );
  }

  Container _textThreeBox() {
    return Container(
      constraints: const BoxConstraints(maxWidth: 500),
      padding: const EdgeInsets.fromLTRB(30, 4, 20, 4),
      decoration: const BoxDecoration(
        border: Border(
          left: BorderSide(color: Colors.black26),
        ),
      ),
      child: Text(
        "Individual Training or Corporate Solutions Offered. Register two or more students and unlock the full potential. Track progress, retrieve certification, organize personnel specific to store levels and be ready for any audit with a single login.",
        style: appTextStyleOne.copyWith(fontSize: 16),
      ),
    );
  }

  Text _textTwo() {
    return const Text(
      "Food Manager Certification at the Comfort of Your Home.",
      style: TextStyle(
        fontSize: 44,
        height: 1.2,
        fontWeight: FontWeight.w700,
        // fontWeight: FontWeight.w900,
        // fontFamily: 'Roboto',
        fontFamily: 'Montserrat',
      ),
    );
  }

  Text _textOne(BuildContext context) {
    return Text(
      "Online Course",
      style: TextStyle(
          color: Theme.of(context).primaryColor, fontWeight: FontWeight.bold),
    );
  }
}
