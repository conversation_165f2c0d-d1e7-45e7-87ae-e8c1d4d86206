import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:wellfed/views/webview/webview.dart';
import '../../controllers/home_ctrl.dart';
import '../../models/cart_model.dart';
import '../../models/checkout_model.dart';
import '../../utils/consts.dart';
import '../../utils/methods.dart';
import '../../utils/router.dart';
import '../../utils/theme.dart';

class CertificationBox extends StatelessWidget {
  const CertificationBox({super.key, required this.size});
  final Size size;

  @override
  Widget build(BuildContext context) {
    bool large = size.width > breakPointLarge;
    bool small = size.width < breakPointMid;
    return Padding(
      padding: !large
          ? EdgeInsets.symmetric(
              horizontal: small ? 28 : size.width * .1,
              vertical: small ? 34 : size.width * .06)
          : homeLargeInsects(size, vertical: size.width * .02),
      child: !small
          ? Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      _image(true),
                    ],
                  ),
                ),
                const SizedBox(width: 30),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _textOne(),
                      const SizedBox(height: 6),
                      _textTwo(),
                      _textThree(),
                      const SizedBox(height: 32),
                      _textFour(),
                      const SizedBox(height: 30),
                      _button(context),
                    ],
                  ),
                ),
              ],
            )
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(child: _image(false)),
                  ],
                ),
                const SizedBox(height: 28),
                _textOne(),
                const SizedBox(height: 6),
                _textTwo(),
                _textThree(),
                const SizedBox(height: 32),
                _textFour(),
                const SizedBox(height: 30),
                _button(context),
              ],
            ),
    );
  }

  ElevatedButton _button(BuildContext context) {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
          elevation: 0,
          backgroundColor: Theme.of(context).primaryColor,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(4))),
      onPressed: () {
        context.go('${Routes.course}/$currentCourseDocId');
        // if (!isLoggedIn()) {
        //   context.go(Routes.login);
        //   return;
        // }
        // context.go(Routes.checkout,
        //     extra: CheckoutModel(checkoutCourses: [
        //       CartCourseModel(courseId: currentCourseDocId)
        //     ], enableEdit: true));
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 16),
        child: GetBuilder<HomeController>(
          init: Get.find<HomeController>(),
          builder: (_) {
            return Text(
              "Enroll Now - \$${_.courseList.firstWhereOrNull((element) => element.docId == currentCourseDocId)?.price ?? ""}",
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            );
          },
        ),
      ),
    );
  }

  Text _textFour() {
    return Text(
      "As a facilitator of Web educational resources, materials and programs to help build and develop quality workforce, WellFed is standing among the industry's best institutes to provide professional training and certification to the next generation.",
      style: appTextStyleTwo.copyWith(
        fontSize: 15,
        height: 2, color: Colors.grey.shade800,
        fontWeight: FontWeight.w500,
        // fontFamily: 'Roboto',
      ),
    );
  }

  Text _textThree() {
    return Text(
      "Professionals.",
      style: appTextStyleTwo.copyWith(
          fontSize: 28, color: appColorTwo, fontWeight: FontWeight.normal),
    );
  }

  Text _textTwo() {
    return Text.rich(
      TextSpan(
          text: "To Build a Community of ",
          style: appTextStyleTwo.copyWith(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            // fontFamily: 'Roboto',
          ),
          children: [
            TextSpan(
              text: "Foodservice  ",
              style: appTextStyleTwo.copyWith(
                  fontSize: 28,
                  color: appColorTwo,
                  fontWeight: FontWeight.normal),
            ),
          ]),
    );
  }

  Text _textOne() {
    return const Text(
      "DELIVERS QUALITY TRAINING",
      style: TextStyle(
          letterSpacing: 1.1,
          wordSpacing: 1.5,
          color: appColorTwo,
          fontSize: 14,
          fontFamily: 'Roboto',
          fontWeight: FontWeight.bold),
    );
  }

  Image _image(bool inRow) {
    return Image.asset(
      'assets/certi.jpg',
      height: inRow ? size.width * .35 : null,
      width: inRow ? size.width * .28 : null,
    );
  }
}
