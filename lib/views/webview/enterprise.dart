import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../utils/consts.dart';
import '../../utils/methods.dart';
import '../../utils/router.dart';
import '../../utils/theme.dart';
import 'webview.dart';

class ForEnterprise extends StatelessWidget {
  const ForEnterprise({super.key, required this.size});
  final Size size;

  @override
  Widget build(BuildContext context) {
    bool large = size.width > breakPointLarge;
    bool small = size.width < breakPointMid;
    return Padding(
      padding: !large
          ? EdgeInsets.symmetric(
              horizontal: small ? 28 : size.width * .1,
              vertical: small ? 34 : size.width * .06)
          : homeLargeInsects(size),
      child: !small
          ? Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _textOne(),
                      const SizedBox(height: 12),
                      _textTwo(),
                      _textThree(),
                      _textFour(),
                      const SizedBox(height: 12),
                      _textFive(),
                      const SizedBox(height: 30),
                      _pointsColumn(),
                      const SizedBox(height: 32),
                      _button(context),
                    ],
                  ),
                ),
                // const SizedBox(width: 20),
                Expanded(
                    child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    _imageStack(true),
                  ],
                ))
              ],
            )
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _textOne(),
                const SizedBox(height: 12),
                _textTwo(),
                _textThree(),
                _textFour(),
                const SizedBox(height: 12),
                _textFive(),
                const SizedBox(height: 30),
                _pointsColumn(),
                const SizedBox(height: 32),
                _button(context),
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _imageStack(false),
                  ],
                ),
              ],
            ),
    );
  }

  Stack _imageStack(bool inRow) {
    return Stack(
      // mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
          child: Image.asset(
            'assets/2.jpg',
            height: inRow ? size.width * .20 : size.width * .5,
            width: inRow ? size.width * .22 : size.width * .5,
          ),
        ),
        Padding(
          padding: EdgeInsets.only(
            top: inRow ? size.width * .15 : size.width * .36,
            left: inRow ? size.width * .14 : size.width * .3,
          ),
          child: Container(
            color: Colors.white,
            height: inRow ? size.width * .16 : size.width * .38,
            width: inRow ? size.width * .20 : size.width * .4,
            padding: const EdgeInsets.all(16),
            child: Image.asset(
              'assets/3.jpg',
              fit: BoxFit.cover,
            ),
          ),
        ),
      ],
    );
  }

  ElevatedButton _button(BuildContext context) {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
          elevation: 0,
          backgroundColor: Theme.of(context).primaryColor,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(4))),
      onPressed: () {
        if (!isLoggedIn()) {
          context.go(Routes.register, extra: true);
          // context.go(Routes.login);
          return;
        }
        context.go('${Routes.course}/$currentCourseDocId');
      },
      child: const Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.0, vertical: 16),
        child: Text(
          "Get Started",
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }

  Column _pointsColumn() {
    return const Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              CupertinoIcons.check_mark_circled_solid,
              color: Colors.green,
            ),
            SizedBox(width: 20),
            Expanded(
              child: Text(
                "Buy in Bulk",
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Roboto',
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 8),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              CupertinoIcons.check_mark_circled_solid,
              color: Colors.green,
            ),
            SizedBox(width: 20),
            Expanded(
              child: Text(
                "Assign Courses",
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Roboto',
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 8),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              CupertinoIcons.check_mark_circled_solid,
              color: Colors.green,
            ),
            SizedBox(width: 20),
            Expanded(
              child: Text(
                "Easy to follow instructions",
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Roboto',
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 8),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              CupertinoIcons.check_mark_circled_solid,
              color: Colors.green,
            ),
            SizedBox(width: 20),
            Expanded(
              child: Text(
                "Multiple Location",
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Roboto',
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 8),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              CupertinoIcons.check_mark_circled_solid,
              color: Colors.green,
            ),
            SizedBox(width: 20),
            Expanded(
              child: Text(
                "Track Progress",
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Roboto',
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 8),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              CupertinoIcons.check_mark_circled_solid,
              color: Colors.green,
            ),
            SizedBox(width: 20),
            Expanded(
              child: Text(
                "Have Access to Your Entire Organization's Certificates with a Single Login",
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Roboto',
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 8),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              CupertinoIcons.check_mark_circled_solid,
              color: Colors.green,
            ),
            SizedBox(width: 20),
            Expanded(
              child: Text(
                "Extra Discounts Available on Volume",
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Roboto',
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Text _textFive() {
    return Text(
      "Take advantage of our incredible offer and seize the opportunity to purchase the same course in bulk, all while enjoying an array of exciting discounts that are sure to exceed your expectations!",
      style: TextStyle(fontSize: 16, height: 1.2, color: Colors.grey.shade700
          // fontWeight: FontWeight.w900,
          // fontFamily: 'Roboto',
          ),
    );
  }

  Text _textFour() {
    return const Text(
      "A single solution for all certification.",
      style: TextStyle(
        fontSize: 30,
        height: 1.2,
        // fontWeight: FontWeight.w900,
        // fontFamily: 'Roboto',
      ),
    );
  }

  Text _textThree() {
    return const Text(
      "Employee Data Management",
      style: TextStyle(
        fontSize: 30,
        height: 1.2, color: appColorTwo,
        fontWeight: FontWeight.w900,
        // fontFamily: 'Roboto',
      ),
    );
  }

  Text _textTwo() {
    return const Text(
      "Experience a hassle free",
      style: TextStyle(
        fontSize: 30,
        height: 1.2,
        // fontWeight: FontWeight.w900,
        // fontFamily: 'Roboto',
      ),
    );
  }

  Text _textOne() {
    return const Text(
      "Enterprise Solutions",
      style: TextStyle(
          letterSpacing: 1.2,
          color: appColorTwo,
          fontFamily: 'Roboto',
          fontWeight: FontWeight.bold),
    );
  }
}
