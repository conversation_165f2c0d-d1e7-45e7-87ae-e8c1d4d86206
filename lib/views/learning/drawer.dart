import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:wellfed/controllers/home_ctrl.dart';
import 'package:wellfed/models/course_model.dart';
import 'package:wellfed/models/dashboard_course.dart';
import 'package:wellfed/models/user_course_model.dart';
import 'package:wellfed/utils/consts.dart';
import 'package:wellfed/views/learning/shared/methods.dart';
import '../../utils/router.dart';

List<ExpansionTileController> tmpCtrls = [];

class LearningDrawer extends StatefulWidget {
  const LearningDrawer(
      {super.key,
      required this.course,
      required this.selectedId,
      required this.small});

  final DashboardCourse? course;
  final String? selectedId;
  final bool small;

  @override
  State<LearningDrawer> createState() => _LearningDrawerState();
}

class _LearningDrawerState extends State<LearningDrawer> {
  @override
  void initState() {
    super.initState();
    tmpCtrls = List.generate(widget.course?.course?.chapters.length ?? 0,
        (index) => ExpansionTileController());
  }

  @override
  Widget build(BuildContext context) {
    return Drawer(
      surfaceTintColor: Colors.white,
      shape: const RoundedRectangleBorder(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // IconButton(
          //     onPressed: () => context.pop(),
          //     icon: const Icon(Icons.arrow_back_rounded)),
          const SizedBox(height: 15),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 4),
            child: ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 180),
                child: _logo(context)),
          ),
          // const Divider(),
          // const Divider(indent: 16, endIndent: 20),
          const SizedBox(height: 12),
          _myCoursesButton(context),
          const SizedBox(height: 12),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // const SizedBox(height: 8),
                  // _goBackTile(context),
                  _overviewTile(context),
                  ...List.generate(widget.course!.course!.chapters.length,
                      (cIndex) {
                    return ChapterTile(
                        expCtrl: tmpCtrls[cIndex],
                        course: widget.course,
                        cIndex: cIndex,
                        selectedId: widget.selectedId,
                        chapter: widget.course!.course!.chapters[cIndex]);
                  }),
                  _certiTile(context, widget.course!.myCourse.certiUrl != null),
                ],
              ),
            ),
          ),
          // const Divider(),
          Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Text(
              //   "Contact",
              //   style: TextStyle(
              //       fontSize: 12,
              //       fontWeight: FontWeight.bold,
              //       color: Colors.grey.shade700),
              // ),
              const SizedBox(height: 12),
              ElevatedButton.icon(
                style: ElevatedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6))),
                onPressed: () async {
                  try {
                    final url = Uri.parse(chatWithUsUrl);
                    if (await canLaunchUrl(url)) {
                      launchUrl(url);
                    }
                  } catch (e) {
                    debugPrint(e.toString());
                  }
                },
                icon: const Icon(CupertinoIcons.chat_bubble_2),
                label: const Text("Chat with us"),
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  IconButton(
                    onPressed: () {},
                    icon: Image.asset(
                      'assets/fb.png',
                      height: 20,
                      color: Colors.grey.shade700,
                    ),
                  ),
                  IconButton(
                    onPressed: () {},
                    icon: Image.asset(
                      'assets/in.png',
                      height: 20,
                      color: Colors.grey.shade700,
                    ),
                  ),
                  IconButton(
                    onPressed: () {},
                    icon: Image.asset(
                      'assets/ln.png',
                      height: 20,
                      color: Colors.grey.shade700,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
            ],
          ),
        ],
      ),
    );
  }

  Padding _myCoursesButton(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: OutlinedButton.icon(
        style: OutlinedButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            side: BorderSide(
                color: Theme.of(context).primaryColor.withOpacity(.1)),
            backgroundColor: Theme.of(context).primaryColor.withOpacity(.03),
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(6))),
        onPressed: () => context.go(Routes.mycourses),
        icon: const Icon(Icons.arrow_back, size: 20),
        label: const Row(
          children: [
            Text("My Courses"),
          ],
        ),
      ),
    );
  }

/*   ListTile _goBackTile(BuildContext context) {
    return ListTile(
      leading: Icon(CupertinoIcons.arrow_left, size: 20),
      // selected: widget.selectedId == null,
      selectedColor: Theme.of(context).primaryColor,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
      onTap: () {
        learningScafKey.currentState?.closeDrawer();
        context.go(Routes.mycourses);
      },
      title: const Text("My Courses"),
      // subtitle: Row(
      //   children: [
      //     // const Icon(CupertinoIcons.timer, size: 18),
      //     // const SizedBox(width: 4),
      //     // Expanded(
      //     //   child: Text(
      //     //     '${widget.course?.course?.duration.hours}h ${widget.course?.course?.duration.minutes}m',
      //     //   ),
      //     // ),
      //   ],
      // ),
    );
  }
 */
  ListTile _overviewTile(BuildContext context) {
    return ListTile(
      leading: widget.selectedId == null
          ? Icon(CupertinoIcons.doc,
              size: 20, color: Theme.of(context).primaryColor)
          : null,
      selected: widget.selectedId == null,
      selectedColor: Theme.of(context).primaryColor,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
      onTap: () {
        learningScafKey.currentState?.closeDrawer();
        Get.find<HomeController>().setSelectedId(null);
      },
      trailing: Icon(
        Icons.arrow_forward_ios_rounded,
        size: 14,
        color: widget.selectedId == null
            ? Colors.transparent
            : Colors.grey.shade400,
      ),
      title: const Text("Overview"),
      subtitle: Row(
        children: [
          const Icon(CupertinoIcons.timer, size: 18),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              '${widget.course?.course?.duration.hours}h ${widget.course?.course?.duration.minutes}m',
            ),
          ),
        ],
      ),
    );
  }

  ListTile _certiTile(BuildContext context, bool available) {
    return ListTile(
      leading: widget.selectedId == "certi"
          ? Icon(Icons.badge_outlined,
              size: 20, color: Theme.of(context).primaryColor)
          : null,
      selected: widget.selectedId == "certi",
      selectedColor: Theme.of(context).primaryColor,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
      onTap: () {
        if (!available) return;
        learningScafKey.currentState?.closeDrawer();
        Get.find<HomeController>().setSelectedId('certi');
      },
      trailing: Icon(
        Icons.arrow_forward_ios_rounded,
        size: 14,
        color: widget.selectedId == "certi"
            ? Colors.transparent
            : Colors.grey.shade400,
      ),
      title: const Text("Certificate"),
      subtitle: Row(
        children: [
          Icon(
              available
                  ? CupertinoIcons.check_mark_circled
                  : CupertinoIcons.lock,
              size: 18),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              available ? 'Available' : 'Pending',
            ),
          ),
        ],
      ),
    );
  }
}

class ChapterTile extends StatelessWidget {
  const ChapterTile({
    super.key,
    required this.expCtrl,
    required this.course,
    required this.selectedId,
    required this.chapter,
    required this.cIndex,
  });

  final ExpansionTileController expCtrl;
  final DashboardCourse? course;
  final String? selectedId;
  final Chapters chapter;
  final int cIndex;

  @override
  Widget build(BuildContext context) {
    // final GlobalKey expansionTileKey = GlobalKey();
    _expandIfSelected();
    return Padding(
      padding: const EdgeInsets.all(2.0),
      child: Material(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
        child: ExpansionTile(
          // key: expansionTileKey,
          // onExpansionChanged: (value) {
          //   if (value) {
          //     _scrollToSelectedContent(expansionTileKey);
          //   }
          // },
          controller: expCtrl,
          // initiallyExpanded:
          //     getChapterIndexFromId(course?.course, selectedId ?? "") == cIndex,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
          title: Text(chapter.name),
          subtitle: Row(
            children: [
              const Icon(CupertinoIcons.timer, size: 18),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  '${chapter.duration.hours}h ${chapter.duration.minutes}m',
                  style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                      color: Colors.grey.shade700),
                ),
              ),
            ],
          ),
          childrenPadding: const EdgeInsets.all(8),
          children: [
            ...List.generate(chapter.modules.length, (mIndex) {
              final module = chapter.modules[mIndex];
              bool selected = selectedId == module.id;
              return ModuleTile(
                selected: selected,
                module: module,
                locked: isLocked(module.id) && !(cIndex == 0 && mIndex == 0),
              );
            }),
            ...List.generate(chapter.assignments.length, (mIndex) {
              final assignment = chapter.assignments[mIndex];
              bool selected = selectedId == assignment.id;
              return AssignTile(
                selected: selected,
                assignment: assignment,
                locked: isLocked(assignment.id),
              );
            })
          ],
        ),
      ),
    );
  }

  void _expandIfSelected() {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      try {
        if (getChapterIndexFromId(course?.course, selectedId ?? "") == cIndex) {
          if (!expCtrl.isExpanded) expCtrl.expand();
        }
      } catch (e) {
        debugPrint(e.toString());
      }
    });
  }

  bool isLocked(String id) {
    if (course?.myCourse.isEnterprise ?? false) {
      return false;
    }
    bool locked = true;
    if (course?.myCourse.progress.keys.contains(id) ?? false) {
      if (course?.myCourse.progress[id] == ProgressType.completed ||
          course?.myCourse.progress[getPreviousId(id)] ==
              ProgressType.completed) {
        locked = false;
      }
    }
    return locked;
  }

  getPreviousId(String id) {
    final ctrl = Get.find<HomeController>();
    final idx = ctrl.selectedIdList.indexOf(id);
    if (idx <= 0) return;
    return ctrl.selectedIdList[idx - 1];
  }
}

class AssignTile extends StatelessWidget {
  const AssignTile({
    super.key,
    required this.selected,
    required this.locked,
    required this.assignment,
  });

  final bool selected;
  final bool locked;
  final Assignments assignment;

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: selected
          ? Icon(CupertinoIcons.flame,
              size: 20, color: Theme.of(context).primaryColor)
          : null,
      selected: selected,
      selectedColor: Theme.of(context).primaryColor,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
      onTap: () {
        if (locked) return;
        learningScafKey.currentState?.closeDrawer();
        Get.find<HomeController>().setSelectedId(assignment.id);
      },
      trailing: Icon(
        locked ? CupertinoIcons.lock : Icons.arrow_forward_ios_rounded,
        size: 14,
        color: selected ? Colors.transparent : Colors.grey.shade500,
      ),
      title: Text(assignment.name),
      subtitle: Row(
        children: [
          const Icon(CupertinoIcons.timer, size: 18),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              '${assignment.duration.hours}h ${assignment.duration.minutes}m',
            ),
          ),
        ],
      ),
    );
  }
}

class ModuleTile extends StatelessWidget {
  const ModuleTile({
    super.key,
    required this.selected,
    required this.locked,
    required this.module,
  });

  final bool selected;
  final bool locked;
  final Modules module;

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: selected
          ? Icon(CupertinoIcons.flame,
              size: 20, color: Theme.of(context).primaryColor)
          : null,
      selected: selected,
      selectedColor: Theme.of(context).primaryColor,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
      onTap: () {
        if (locked) return;
        learningScafKey.currentState?.closeDrawer();
        Get.find<HomeController>().setSelectedId(module.id);
      },
      trailing: Icon(
        locked ? CupertinoIcons.lock : Icons.arrow_forward_ios_rounded,
        size: 14,
        color: selected ? Colors.transparent : Colors.grey.shade500,
      ),
      title: Text(module.name),
      subtitle: Row(
        children: [
          const Icon(CupertinoIcons.timer, size: 18),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              '${module.duration.hours}h ${module.duration.minutes}m',
            ),
          ),
        ],
      ),
    );
  }
}

void _scrollToSelectedContent(GlobalKey expansionTileKey) {
  final keyContext = expansionTileKey.currentContext;
  if (keyContext != null) {
    Future.delayed(const Duration(milliseconds: 200)).then((value) {
      Scrollable.ensureVisible(keyContext,
          duration: const Duration(milliseconds: 200));
    });
  }
}

Widget _logo(BuildContext context) => InkWell(
    hoverColor: Colors.transparent,
    splashColor: Colors.transparent,
    highlightColor: Colors.transparent,
    onTap: () => context.go(Routes.home),
    child: LimitedBox(maxWidth: 120, child: Image.asset('assets/logo.png')));
