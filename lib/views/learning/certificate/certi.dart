import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:wellfed/services/docs/gen.dart';
import 'package:wellfed/utils/consts.dart';
import 'package:wellfed/utils/firebase.dart';
import 'package:wellfed/utils/loaders.dart';
import '../../../models/certi_model.dart';
import '../../../models/dashboard_course.dart';
import '../../../utils/router.dart';

class CertiPage extends StatelessWidget {
  const CertiPage({super.key, required this.course});
  final DashboardCourse course;

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<CertiModel>(
        future: FBFireStore.certis
            .doc(course.myCourse.docId)
            .get()
            .then((value) => CertiModel.fromSnap(value)),
        builder: (context, snapshot) {
          if (snapshot.hasError) {
            debugPrint(snapshot.error.toString());
            return const Center(child: Text("Something went wrong!"));
          }
          if (snapshot.hasData) {
            return snapshot.data == null
                ? const Center(child: Text("Something went wrong!"))
                : SingleChildScrollView(
                    padding: const EdgeInsets.all(20.0),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            _copyButton(),
                            ElevatedButton.icon(
                                style: ElevatedButton.styleFrom(
                                    backgroundColor:
                                        Theme.of(context).primaryColor,
                                    shape: RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(4))),
                                onPressed: () =>
                                    onDownload(context, snapshot.data!),
                                icon: const Icon(
                                  Icons.picture_as_pdf,
                                  color: Colors.white,
                                ),
                                label: const Padding(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 8.0, vertical: 12),
                                  child: Text(
                                    "Download",
                                    style: TextStyle(color: Colors.white),
                                  ),
                                ))
                          ],
                        ),
                        const SizedBox(height: 12),
                        CertiPainter(
                          certiModel: snapshot.data!,
                        ),
                      ],
                    ),
                  );
          }
          return Center(
              child: loaderWave(color: Theme.of(context).primaryColor));
        });
  }

  StatefulBuilder _copyButton() {
    bool copied = false;
    return StatefulBuilder(builder: (context, setState2) {
      return TextButton.icon(
        onPressed: () async {
          await _onCopy();
          setState2(() => copied = true);
          await Future.delayed(const Duration(milliseconds: 1500));
          setState2(() => copied = false);
        },
        icon: Icon(
          copied ? CupertinoIcons.check_mark_circled : CupertinoIcons.link,
          size: 20,
          color: copied ? Colors.green : null,
        ),
        label: Text(
          copied ? "Copied" : "Copy",
          style: TextStyle(color: copied ? Colors.green : null),
        ),
      );
    });
  }

  _onCopy() async {
    try {
      await Clipboard.setData(ClipboardData(
          text: '$currentHostUrl${Routes.certi}/${course.myCourse.docId}'));
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  onDownload(BuildContext context, CertiModel certiModel) async {
    try {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
            await Future.delayed(const Duration(milliseconds: 500));
            DocGen.generateCerti(certiModel).then((value) => context.pop());
          });
          return const AlertDialog(
            title: Text("Please Wait"),
            content: Text("Processing certificate..."),
          );
        },
      );
    } catch (e) {
      debugPrint(e.toString());
    }
  }
}

class CertiPainter extends StatelessWidget {
  const CertiPainter({
    super.key,
    required this.certiModel,
  });
  final CertiModel certiModel;
  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: certiWidth / certiHeight,
      child: DocGen.generateCertiWid(certiModel),
    );
  }
}
