import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wellfed/controllers/home_ctrl.dart';
import 'package:wellfed/models/course_model.dart';
import 'package:wellfed/models/dashboard_course.dart';
import 'package:wellfed/utils/other.dart';
import 'package:wellfed/utils/theme.dart';
import 'package:wellfed/views/learning/shared/widgets.dart';
import '../../../models/user_course_model.dart';
import '../../../utils/firebase.dart';
import '../../../utils/methods.dart';
import '../content.dart';
import '../shared/methods.dart';

class ModuleWid extends StatelessWidget {
  const ModuleWid({
    super.key,
    required this.course,
    required this.module,
    required this.small,
  });
  final Modules module;
  final bool small;
  final DashboardCourse course;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(
          horizontal: small ? 20.0 : 40, vertical: small ? 20.0 : 40),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Text(
            module.name,
            style: appTitleTextStyle.copyWith(
                fontWeight: FontWeight.bold, fontSize: 18),
          ),
          Text(
            '${module.duration.hours}h ${module.duration.minutes}m',
          ),
          const SizedBox(height: 20),
          Text(module.content),
          const SizedBox(height: 20),
          course.myCourse.isEnterprise
              ? Align(
                  alignment: Alignment.center,
                  child: Container(
                    padding: const EdgeInsets.all(20.0),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(6),
                        color: Colors.grey.shade200),
                    child: const Text("Video Content"),
                  ),
                )
              : LimitedBox(
                  maxHeight: 600,
                  child: ApiVideoPlayerWid(
                      key: Key(module.id), videoLink: module.videoUrl),
                ),
          const SizedBox(height: 40),
          if (!course.myCourse.completed)
            Align(
                alignment: Alignment.center,
                child: NextAndPrev(
                  key: Key(module.id),
                  waitingTime: course.myCourse.isEnterprise
                      ? 0
                      : !course.myCourse.progress.keys
                              .contains(getNextId(module.id))
                          ? module.minWatch
                          : 0,
                  enableNext: true,
                  onNext: () async {
                    if (course.myCourse.isEnterprise) return;
                    verifyStatus(module.id);
                    if (isLastId(module.id)) {
                      await completeThisCourse();
                      if (context.mounted) {
                        showAppSnackBar(context, "Congratulations",
                            "Course completed successfully. You can download the certificate");
                      }
                      if (course.course?.examType == ExamTypes.online) {
                        Get.find<HomeController>().setSelectedId("certi");
                      }
                    }
                  },
                )),
        ],
      ),
    );
  }

  completeThisCourse() async {
    try {
      int scored = 0;
      int total = 0;
      course.myCourse.asignScores.forEach((key, value) {
        final vals = value.toString().split("/");
        scored += int.parse(vals[0]);
        total += int.parse(vals[1]);
      });
      // final certiDoc = await FBFireStore.certis.add({
      //   'time': FieldValue.serverTimestamp(),
      //   'uid': FBAuth.auth.currentUser?.uid,
      //   'uCourseId': widget.course.myCourse.docId,
      //   'courseId': widget.course.course?.docId,
      //   'name': widget.course.course?.docId,
      // });
      // await FBFireStore.userCourses.doc(course.myCourse.docId).update({
      //   if (course.course?.examType == ExamTypes.online)
      //     "score": '$scored/$total',
      //   "completed": true,
      //   // 'certiUrl': certiDoc.id,
      // });
      final batch = FBFireStore.fs.batch();
      if (course.course?.examType == ExamTypes.online) {
        batch.set(FBFireStore.certis.doc(course.myCourse.docId), {
          if (course.course?.examType == ExamTypes.online)
            "score": '$scored/$total',
          "fullName": Get.find<HomeController>().userData?.name,
          "courseName": course.course?.title,
          "courseLastUpdated": course.course?.updatedOn,
          "completedOn": FieldValue.serverTimestamp(),
          "validForDays": course.course?.certiValidity,
          "hours":
              '${course.course?.duration.hours}h:${course.course?.duration.hours}m',
          "uid": FBAuth.auth.currentUser?.uid,
          "uCourseId": course.myCourse.docId,
          "courseId": course.course?.docId,
        });
      }
      batch.update(FBFireStore.userCourses.doc(course.myCourse.docId), {
        "completed": true,
        "completedOn": FieldValue.serverTimestamp(),
        if (course.course?.examType == ExamTypes.online)
          "score": '$scored/$total',
        if (course.course?.examType == ExamTypes.online)
          'certiUrl': course.myCourse.docId,
      });
      await batch.commit();
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  verifyStatus(String id) async {
    if (course.myCourse.progress.keys.contains(id)) {
      if (course.myCourse.progress[id] == ProgressType.completed) return;
    }
    final nextId = getNextId(id);
    await updateStatus(course.myCourse.docId, {
      "progress.$id": ProgressType.completed,
      if (nextId != null) "progress.$nextId": ProgressType.inProgress,
    });
  }
}
