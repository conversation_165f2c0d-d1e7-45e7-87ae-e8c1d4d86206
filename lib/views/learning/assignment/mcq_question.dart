import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:wellfed/models/course_model.dart';

class MCQWid extends StatefulWidget {
  const MCQWid(
      {super.key,
      required this.mcq,
      required this.indx,
      required this.showAnswers,
      required this.enabled});
  final Mcqs mcq;
  final int indx;
  final bool showAnswers;
  final bool enabled;

  @override
  State<MCQWid> createState() => _MCQWidState();
}

class _MCQWidState extends State<MCQWid> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 20),
        Text.rich(TextSpan(
            text: 'Question ${widget.indx + 1}: ',
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            children: [
              TextSpan(
                text: widget.mcq.question,
                style: const TextStyle(
                    fontSize: 16, fontWeight: FontWeight.normal),
              )
            ])),
        ...List.generate(
            widget.mcq.options.length,
            (index) => RadioListTile<String>.adaptive(
                  shape: RoundedRectangleBorder(
                    side: BorderSide(
                        color: widget.showAnswers &&
                                widget.mcq.options[index] == widget.mcq.answer
                            ? Colors.green
                            : Colors.transparent),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  tileColor: widget.showAnswers &&
                          widget.mcq.options[index] == widget.mcq.answer
                      ? MaterialStateColor.resolveWith(
                          (states) => Colors.green.shade100)
                      : null,
                  value: widget.mcq.options[index],
                  title: Text(widget.mcq.options[index]),
                  groupValue: widget.mcq.selectedAnswer?.text,
                  onChanged: (value) => setState(() {
                    if (!widget.enabled) return;
                    widget.mcq.selectedAnswer?.text = value!;
                  }),
                ))
      ],
    );
  }
}
