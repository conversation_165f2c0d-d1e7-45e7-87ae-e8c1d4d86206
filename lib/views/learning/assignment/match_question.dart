import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:wellfed/models/course_model.dart';
import 'package:wellfed/utils/consts.dart';

class MatchWid extends StatefulWidget {
  const MatchWid(
      {super.key,
      required this.match,
      required this.indx,
      required this.enabled,
      required this.showAnswers});
  final Matches match;
  final int indx;
  final bool showAnswers;
  final bool enabled;

  @override
  State<MatchWid> createState() => _MatchWidState();
}

class _MatchWidState extends State<MatchWid> {
  List<Pairs> pairs = <Pairs>[];
  List<String> rightVals = <String>[];
  @override
  void initState() {
    super.initState();
    pairs.addAll(widget.match.selectedPairs ?? []);
    for (var element in pairs) {
      rightVals.add(element.right);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 20),
        Text.rich(TextSpan(
            text: 'Question ${widget.indx + 1}: ',
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            children: [
              TextSpan(
                text: widget.match.question,
                style: const TextStyle(
                    fontSize: 16, fontWeight: FontWeight.normal),
              )
            ])),
        const SizedBox(height: 20),
        if (!widget.showAnswers) const Text(howMatchWorks),
        if (!widget.showAnswers) const SizedBox(height: 20),
        Row(
          children: [
            Expanded(
              child: ListView.builder(
                physics: const ClampingScrollPhysics(),
                shrinkWrap: true,
                itemBuilder: (context, index) {
                  return Card(
                    elevation: widget.enabled ? null : 0,
                    surfaceTintColor: Colors.white,
                    child: ListTile(
                      leading: CircleAvatar(
                        radius: 12,
                        backgroundColor: Colors.grey.shade200,
                        child: Text(
                          '${index + 1}',
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                      trailing: Icon(
                        Icons.double_arrow_rounded,
                        size: 20,
                        color: Colors.grey.shade400,
                      ),
                      title: Text(widget.match.selectedPairs![index].left),
                    ),
                  );
                },
                itemCount: widget.match.selectedPairs?.length ?? 0,
              ),
            ),
            Expanded(
              child: ReorderableList(
                physics: const ClampingScrollPhysics(),
                shrinkWrap: true,
                onReorderEnd: (index) {},
                onReorderStart: (index) {},
                itemBuilder: (context, index) {
                  return ReorderableDragStartListener(
                    enabled: widget.enabled,
                    key: Key('$index'),
                    index: index,
                    child: Card(
                      elevation: widget.enabled ? null : 0,
                      child: ListTile(
                        title: Text(rightVals[index]),
                        trailing: const Icon(CupertinoIcons.line_horizontal_3),
                      ),
                    ),
                  );
                },
                itemCount: rightVals.length,
                onReorder: (oldIndex, newIndex) {
                  if (!widget.enabled) return;
                  setState(() {
                    if (oldIndex < newIndex) {
                      newIndex -= 1;
                    }
                    final String item = rightVals.removeAt(oldIndex);
                    rightVals.insert(newIndex, item);
                    regeneratePairs();
                  });
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 20),
        if (widget.showAnswers)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Divider(indent: 8, endIndent: 8),
              const SizedBox(height: 10),
              const Center(
                child: Text(
                  "Match Answers",
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
              ),
              const SizedBox(height: 10),
              Text(
                'Answer ${widget.indx + 1}: ${widget.match.question}',
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 20),
              ListView.builder(
                physics: const ClampingScrollPhysics(),
                shrinkWrap: true,
                itemBuilder: (context, index) {
                  return Card(
                    surfaceTintColor: Colors.white,
                    child: ListTile(
                      leadingAndTrailingTextStyle: const TextStyle(),
                      leading: CircleAvatar(
                        radius: 12,
                        backgroundColor: Colors.grey.shade200,
                        child: Text(
                          '${index + 1}',
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                      trailing: Text(widget.match.selectedPairs![index].right),
                      title: Text(widget.match.selectedPairs![index].left),
                    ),
                  );
                },
                itemCount: widget.match.selectedPairs?.length ?? 0,
              ),
            ],
          ),
      ],
    );
  }

  regeneratePairs() {
    pairs.clear();
    for (var i = 0; i < (widget.match.selectedPairs?.length ?? 0); i++) {
      pairs.add(Pairs(
          left: widget.match.selectedPairs![i].left, right: rightVals[i]));
    }
    widget.match.selectedPairs?.clear();
    widget.match.selectedPairs?.addAll(pairs);
  }
}

class MovableList extends StatefulWidget {
  const MovableList({
    super.key,
    required this.match,
    required this.updateSelectedPair,
  });

  final Matches match;
  final Function updateSelectedPair;

  @override
  State<MovableList> createState() => _MovableListState();
}

class _MovableListState extends State<MovableList> {
  final List<String> _items = <String>[];
  @override
  void initState() {
    super.initState();
    widget.match.selectedPairs?.forEach((element) {
      _items.add(element.right);
    });
  }

  @override
  Widget build(BuildContext context) {
    return ReorderableList(
      physics: const ClampingScrollPhysics(),
      shrinkWrap: true,
      onReorderEnd: (index) {},
      onReorderStart: (index) {},
      itemBuilder: (context, index) {
        return ReorderableDragStartListener(
          key: Key('$index'),
          index: index,
          child: Card(
            child: ListTile(
              title: Text(_items[index]),
              trailing: const Icon(CupertinoIcons.line_horizontal_3),
            ),
          ),
        );
      },
      itemCount: _items.length,
      onReorder: (oldIndex, newIndex) {
        setState(() {
          if (oldIndex < newIndex) {
            newIndex -= 1;
          }
          final String item = _items.removeAt(oldIndex);
          _items.insert(newIndex, item);
          widget.updateSelectedPair(newIndex, item);
        });
      },
    );
  }
}
