import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wellfed/models/course_model.dart';
import 'package:wellfed/utils/firebase.dart';
import 'package:wellfed/utils/methods.dart';
import 'package:wellfed/utils/other.dart';
import 'package:wellfed/views/learning/assignment/match_question.dart';
import 'package:wellfed/views/learning/assignment/mcq_question.dart';
import 'package:wellfed/views/learning/shared/widgets.dart';
import '../../../controllers/home_ctrl.dart';
import '../../../models/dashboard_course.dart';
import '../../../models/user_course_model.dart';
import '../../../utils/theme.dart';
import '../content.dart';
import '../shared/methods.dart';

class AssignWid extends StatefulWidget {
  const AssignWid(
      {super.key,
      required this.assignment,
      required this.small,
      required this.course});
  final Assignments assignment;
  final bool small;
  final DashboardCourse course;

  @override
  State<AssignWid> createState() => _AssignWidState();
}

class _AssignWidState extends State<AssignWid> {
  String assignId = "";
  List<Mcqs> mcqs = [];
  List<Matches> matches = [];
  bool showAnswers = false;
  bool showResult = false;
  bool enabled = true;
  List<List<bool>> matchAnswer = [];
  List<bool> mcqAnswer = [];
  String finalScore = "";
  bool passed = false;
  bool alreadyPassed = false;

  @override
  void initState() {
    super.initState();
    setUpQuestions();
    checkIfPassed();
    print(assignId);
  }

  checkIfPassed() {
    if (widget.course.myCourse.progress[assignId] == ProgressType.completed) {
      alreadyPassed = true;
      passed = true;
      enabled = false;
      showAnswers = true;
      finalScore = widget.course.myCourse.asignScores[assignId];
      if (mounted) {
        setState(() {});
      }
    }
  }

  setUpQuestions() {
    showAnswers = false;
    enabled = true;
    showResult = false;
    assignId = widget.assignment.id;
    mcqs.clear();
    matches.clear();
    for (var element in widget.assignment.mcqs) {
      mcqs.add(element.copyWith(txtCtrl: TextEditingController()));
    }
    for (var element in widget.assignment.matches) {
      matches.add(element.copyWith(newPairs: getRandomPairs(element)));
    }
  }

  List<Pairs> getRandomPairs(Matches matchModel) {
    List<String> rightVales = [];
    List<Pairs> newPairs = [];
    for (var element in matchModel.pairs) {
      rightVales.add(element.right);
    }
    rightVales.shuffle();
    for (var i = 0; i < rightVales.length; i++) {
      newPairs.add(Pairs(left: matchModel.pairs[i].left, right: rightVales[i]));
    }
    return newPairs;
  }

  @override
  Widget build(BuildContext context) {
    if (widget.assignment.id != assignId) setUpQuestions();
    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(
          horizontal: widget.small ? 20.0 : 40,
          vertical: widget.small ? 20.0 : 40),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Text(
            widget.assignment.name,
            style: appTitleTextStyle.copyWith(
                fontWeight: FontWeight.bold, fontSize: 18),
          ),
          Text(
            '${widget.assignment.duration.hours}h ${widget.assignment.duration.minutes}m',
          ),
          // const SizedBox(height: 20),
          if (mcqs.isNotEmpty)
            ...List.generate(
              mcqs.length,
              (index) => MCQWid(
                mcq: mcqs[index],
                indx: index,
                enabled: enabled,
                showAnswers: showAnswers,
              ),
            ),
          if (matches.isNotEmpty)
            ...List.generate(
              matches.length,
              (index) => MatchWid(
                match: matches[index],
                indx: index,
                enabled: enabled,
                showAnswers: showAnswers,
              ),
            ),
          const SizedBox(height: 40),
          if (alreadyPassed)
            Center(
              child: Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: Column(
                  children: [
                    Text(
                      'Final Score $finalScore',
                      style: const TextStyle(
                          fontWeight: FontWeight.bold, fontSize: 16),
                    ),
                    Text(
                      'Percantage Obtained: ${getPercentage().toStringAsFixed(2)}%',
                      style: const TextStyle(
                          fontWeight: FontWeight.bold, fontSize: 16),
                    ),
                    Text(
                      'Minimum Percantage Required: ${double.parse(widget.assignment.minPercentage)}%',
                      style: const TextStyle(
                          fontWeight: FontWeight.bold, fontSize: 16),
                    ),
                  ],
                ),
              ),
            ),
          if (showResult)
            Center(
              child: Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: Column(
                  children: [
                    if (mcqAnswer.isNotEmpty)
                      Text(
                        "MCQ Score: ${mcqAnswer.where((element) => element).length}/${mcqAnswer.length}",
                        style: const TextStyle(
                            fontWeight: FontWeight.bold, fontSize: 16),
                      ),
                    if (matchAnswer.isNotEmpty)
                      ...List.generate(
                        matchAnswer.length,
                        (index) => Text(
                          "Match Score ${matchAnswer.length > 1 ? index + 1 : ""}: ${matchAnswer[index].where((element) => element).length}/${matchAnswer[index].length}",
                          style: const TextStyle(
                              fontWeight: FontWeight.bold, fontSize: 16),
                        ),
                      ),
                    if (matchAnswer.isNotEmpty && mcqAnswer.isNotEmpty)
                      Text(
                        'Final Score $finalScore',
                        style: const TextStyle(
                            fontWeight: FontWeight.bold, fontSize: 16),
                      ),
                    Text(
                      'Percantage Obtained: ${getPercentage().toStringAsFixed(2)}%',
                      style: const TextStyle(
                          fontWeight: FontWeight.bold, fontSize: 16),
                    ),
                    Text(
                      'Minimum Percantage Required: ${double.parse(widget.assignment.minPercentage)}%',
                      style: const TextStyle(
                          fontWeight: FontWeight.bold, fontSize: 16),
                    ),
                  ],
                ),
              ),
            ),
          if (!passed && !widget.course.myCourse.isEnterprise)
            Align(
              alignment: Alignment.center,
              child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                      minimumSize: const Size(250, 50),
                      backgroundColor: Theme.of(context).primaryColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      )),
                  onPressed: () {
                    if (showAnswers) return;
                    matchAnswer.clear();
                    mcqAnswer.clear();
                    for (var element in mcqs) {
                      final bool ans = element.isCorrect();
                      mcqAnswer.add(ans);
                    }
                    for (var element in matches) {
                      List<bool> lst = <bool>[];
                      for (var i = 0; i < element.pairs.length; i++) {
                        final bool ans = element.pairs[i]
                            .isSameAs(element.selectedPairs![i]);
                        lst.add(ans);
                      }
                      matchAnswer.add(lst);
                    }
                    finalScore = getFinalScore();
                    passed = getPercentage() >=
                        double.parse(widget.assignment.minPercentage);
                    debugPrint("Passed: $passed");
                    if (passed) {
                      setState(() {
                        showAnswers = true;
                        enabled = false;
                      });
                    } else {
                      setState(() {
                        showResult = true;
                      });
                    }
                    _showResultDialog(context, passed);
                  },
                  child: const Text(
                    "Submit",
                    style: TextStyle(color: Colors.white),
                  )),
            ),
          const SizedBox(height: 40),
          if (!widget.course.myCourse.completed)
            Align(
                alignment: Alignment.center,
                child: NextAndPrev(
                  key: Key(assignId),
                  enableNext: widget.course.myCourse.isEnterprise
                      ? true
                      : passed && !widget.course.myCourse.completed,
                  onNext: _onNextTap,
                )),
        ],
      ),
    );
  }

  _onNextTap() async {
    if (widget.course.myCourse.isEnterprise) return;
    await verifyStatus(assignId);
    try {
      if (isLastId(assignId)) {
        await completeThisCourse();
        if (context.mounted) {
          showAppSnackBar(context, "Congratulations",
              "Course completed successfully. ${widget.course.course?.examType == ExamTypes.online ? "You can download the certificate." : "You can schedule your exam."}");
        }
        if (widget.course.course?.examType == ExamTypes.online) {
          Get.find<HomeController>().setSelectedId("certi");
        }
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  completeThisCourse() async {
    try {
      int scored = 0;
      int total = 0;
      widget.course.myCourse.asignScores.forEach((key, value) {
        final vals = value.toString().split("/");
        scored += int.parse(vals[0]);
        total += int.parse(vals[1]);
      });
      final batch = FBFireStore.fs.batch();
      if (widget.course.course?.examType == ExamTypes.online) {
        batch.set(FBFireStore.certis.doc(widget.course.myCourse.docId), {
          if (widget.course.course?.examType == ExamTypes.online)
            "score": '$scored/$total',
          "fullName": Get.find<HomeController>().userData?.name,
          "courseName": widget.course.course?.title,
          "courseLastUpdated": widget.course.course?.updatedOn,
          "completedOn": FieldValue.serverTimestamp(),
          "validForDays": widget.course.course?.certiValidity,
          "hours":
              '${widget.course.course?.duration.hours}h:${widget.course.course?.duration.hours}m',
          "uid": FBAuth.auth.currentUser?.uid,
          "uCourseId": widget.course.myCourse.docId,
          "courseId": widget.course.course?.docId,
        });
      }
      batch.update(FBFireStore.userCourses.doc(widget.course.myCourse.docId), {
        "completed": true,
        "completedOn": FieldValue.serverTimestamp(),
        if (widget.course.course?.examType == ExamTypes.online)
          "score": '$scored/$total',
        if (widget.course.course?.examType == ExamTypes.online)
          'certiUrl': widget.course.myCourse.docId,
      });
      await batch.commit();
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  verifyStatus(String id) async {
    if (widget.course.myCourse.progress.keys.contains(id)) {
      if (widget.course.myCourse.progress[id] == ProgressType.completed) return;
    }
    final nextId = getNextId(id);
    await updateStatus(widget.course.myCourse.docId, {
      "asignScores.$id": finalScore,
      "progress.$id": ProgressType.completed,
      if (nextId != null) "progress.$nextId": ProgressType.inProgress,
    });
  }

  Future<dynamic> _showResultDialog(BuildContext context, bool passed) {
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Center(child: Text("Result")),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Final Score: $finalScore',
              style: const TextStyle(),
            ),
            Text(
              'Percantage Obtained: ${getPercentage().toStringAsFixed(2)}%',
              style: const TextStyle(),
            ),
            Text(
              'Minimum Percantage Required: ${double.parse(widget.assignment.minPercentage)}%',
              style: const TextStyle(),
            ),
            Text.rich(TextSpan(text: "Status: ", children: [
              TextSpan(
                  text: passed ? "Qualified" : "Not Qualified",
                  style: TextStyle(color: passed ? Colors.green : Colors.red))
            ])),
          ],
        ),
        actions: [
          Center(
            child: ElevatedButton(
                onPressed: () {
                  if (passed) {
                    _onNextTap();
                  }
                  Navigator.of(context).pop();
                },
                child: const Text("Okay")),
          )
        ],
      ),
    );
  }

  double getPercentage() {
    return (int.parse(finalScore.split("/").first) /
            int.parse(finalScore.split("/").last)) *
        100;
  }

  String getFinalScore() {
    int totalMarks = 0;
    int scoredMarks = 0;
    for (var i = 0; i < mcqAnswer.length; i++) {
      totalMarks++;
      if (mcqAnswer[i]) scoredMarks++;
    }
    for (var i = 0; i < matchAnswer.length; i++) {
      for (var j = 0; j < matchAnswer[i].length; j++) {
        totalMarks++;
        if (matchAnswer[i][j]) scoredMarks++;
      }
    }
    return '$scoredMarks/$totalMarks';
  }
}
