import 'package:get/get.dart';
import 'package:wellfed/controllers/home_ctrl.dart';
import 'package:wellfed/models/course_model.dart';

int getChapterIndexFromId(CourseModel? course, String id) {
  int index = -1;
  if (course == null) return -1;
  for (var i = 0; i < course.chapters.length; i++) {
    for (var j = 0; j < course.chapters[i].modules.length; j++) {
      if (course.chapters[i].modules[j].id == id) return i;
    }
    for (var k = 0; k < course.chapters[i].assignments.length; k++) {
      if (course.chapters[i].assignments[k].id == id) return i;
    }
  }
  return index;
}

String? getNextId(String id) {
  final ctrl = Get.find<HomeController>();
  final idx = ctrl.selectedIdList.indexOf(id);
  if (idx >= ctrl.selectedIdList.length - 1) return null;
  return ctrl.selectedIdList[idx + 1];
}

bool isLastId(String id) =>
    Get.find<HomeController>().selectedIdList.last == id;
