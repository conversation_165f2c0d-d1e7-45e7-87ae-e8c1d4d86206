import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:video_player/video_player.dart';
import 'package:wellfed/controllers/home_ctrl.dart';
import 'package:chewie/chewie.dart';

class ApiVideoPlayerWid extends StatefulWidget {
  const ApiVideoPlayerWid({super.key, required this.videoLink});
  final String videoLink;

  @override
  State<ApiVideoPlayerWid> createState() => _ApiVideoPlayerWidState();
}

class _ApiVideoPlayerWidState extends State<ApiVideoPlayerWid> {
  // final videoPlayerController = VideoPlayerController.networkUrl(Uri.parse(
  //     'https://flutter.github.io/assets-for-api-docs/assets/videos/butterfly.mp4'));
  late VideoPlayerController _videoController;
  late ChewieController _chewieController;

  Duration _currentPosition = Duration.zero;
  bool _isPlaying = false;

  void _initControllers() {
    _videoController = VideoPlayerController.networkUrl(
        Uri.parse(widget.videoLink))
      // Uri.parse(\
      // 'https://vod.api.video/vod/vi5aquhuo41KnZI8FsnBcMDu/mp4/source.mp4'))
      ..initialize().then((value) {
        _videoController.seekTo(_currentPosition);
        setState(() {});
      });
    _chewieController = ChewieController(
      aspectRatio: 16 / 9,
      videoPlayerController: _videoController,
    )..addListener(_reInitListener);
    if (_isPlaying) {
      _chewieController.play();
    }
  }

  void _reInitControllers() {
    _chewieController.removeListener(_reInitListener);
    _currentPosition = _videoController.value.position;
    _isPlaying = _chewieController.isPlaying;
    _initControllers();
  }

  void _reInitListener() {
    if (!_chewieController.isFullScreen) {
      _reInitControllers();
    }
  }

  @override
  void initState() {
    super.initState();
    _initControllers();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: _videoController.value.isInitialized
          ? Chewie(controller: _chewieController)
          : null,
    );
  }

  @override
  void dispose() {
    _videoController.dispose();
    _chewieController.dispose();
    super.dispose();
  }
}

class NextAndPrev extends StatefulWidget {
  const NextAndPrev(
      {super.key,
      this.waitingTime = 0,
      required this.enableNext,
      required this.onNext});

  final int waitingTime;
  final bool enableNext;
  final Function onNext;

  @override
  State<NextAndPrev> createState() => _NextAndPrevState();
}

class _NextAndPrevState extends State<NextAndPrev> {
  RxInt time = 0.obs;
  @override
  void initState() {
    super.initState();
    time.value = widget.waitingTime;
    _timer();
  }

  _timer() {
    Timer.periodic(const Duration(seconds: 1), (timer) {
      time--;
      if (time <= 0) timer.cancel();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        MaterialButton(
          color: Colors.grey.shade100,
          onPressed: () {
            final ctrl = Get.find<HomeController>();
            final idx = ctrl.selectedIdList.indexOf(ctrl.selectedId ?? "");
            if (idx <= 0) return;
            ctrl.setSelectedId(ctrl.selectedIdList[idx - 1]);
          },
          padding: const EdgeInsets.fromLTRB(20, 18, 24, 18),
          shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.horizontal(
                  left: Radius.circular(45), right: Radius.circular(1))),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Icon(
                CupertinoIcons.arrow_left_circle_fill,
                size: 20,
                color: Colors.grey.shade700,
              ),
              const SizedBox(width: 6),
              const Text(
                "Previous",
                style: TextStyle(fontSize: 16),
              ),
            ],
          ),
        ),
        const SizedBox(width: 2),
        MaterialButton(
          hoverElevation: widget.enableNext ? null : 0,
          focusElevation: widget.enableNext ? null : 0,
          elevation: widget.enableNext ? null : 0,
          enableFeedback: widget.enableNext ? true : false,
          highlightElevation: widget.enableNext ? null : 0,
          splashColor: widget.enableNext ? null : Colors.transparent,
          hoverColor: widget.enableNext ? null : Colors.transparent,
          color: Colors.grey.shade100,
          highlightColor: widget.enableNext ? null : Colors.transparent,
          onPressed: () {
            if (time > 0 || !widget.enableNext) return;
            final ctrl = Get.find<HomeController>();
            final idx = ctrl.selectedIdList.indexOf(ctrl.selectedId ?? "");
            widget.onNext();
            if (idx >= ctrl.selectedIdList.length - 1) return;
            ctrl.setSelectedId(ctrl.selectedIdList[idx + 1]);
          },
          padding: const EdgeInsets.fromLTRB(24, 18, 20, 18),
          shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.horizontal(
                  right: Radius.circular(45), left: Radius.circular(1))),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const Text(
                "Continue",
                style: TextStyle(fontSize: 16),
              ),
              const SizedBox(width: 6),
              Obx(() => time.value > 0
                  ? Text('${time}s')
                  : Icon(
                      CupertinoIcons.arrow_right_circle_fill,
                      size: 20,
                      color: Colors.grey.shade700,
                    )),
            ],
          ),
        ),
      ],
    );
  }
}
