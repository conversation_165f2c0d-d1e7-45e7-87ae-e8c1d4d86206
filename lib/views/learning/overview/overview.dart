import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wellfed/controllers/home_ctrl.dart';
import 'package:wellfed/models/dashboard_course.dart';
import 'package:wellfed/models/user_course_model.dart';
import 'package:wellfed/utils/methods.dart';
import 'package:wellfed/utils/theme.dart';

import '../../../utils/other.dart';

class OverviewWid extends StatelessWidget {
  const OverviewWid({super.key, required this.course});

  final DashboardCourse? course;

  @override
  Widget build(BuildContext context) {
    return course == null
        ? const Center(child: Text("No Data Found!"))
        : Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 12),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                            height: 100,
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: AspectRatio(
                                aspectRatio: 16 / 9,
                                child: FadeInImage.memoryNetwork(
                                    placeholderErrorBuilder:
                                        (context, error, stackTrace) =>
                                            Image.memory(placeholderGrad),
                                    placeholder: placeholderGrad,
                                    fit: BoxFit.cover,
                                    image: course!.course!.imageUrl),
                              ),
                            ),
                          ),
                          const SizedBox(width: 20),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  course!.course!.title,
                                  style: appTextStyleTwo.copyWith(
                                      fontSize: 24,
                                      fontWeight: FontWeight.bold),
                                ),
                                Row(
                                  children: [
                                    const Icon(
                                      CupertinoIcons.person,
                                      size: 14,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(course!.course!.author)
                                  ],
                                ),
                                const SizedBox(height: 12),
                                Text(course!.course!.desc),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      const Text("Overview",
                          style: TextStyle(fontWeight: FontWeight.bold)),
                      const SizedBox(height: 4),
                      Text(course!.course!.overview),
                      const SizedBox(height: 8),
                      Text.rich(
                        TextSpan(
                          text: "Last Updated on ",
                          style: TextStyle(
                              color: Colors.green.shade700, fontSize: 16),
                          children: [
                            TextSpan(
                              text: course!.course!.updatedOn.goodDate(),
                              style: TextStyle(
                                  color: Colors.green.shade800,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          const Icon(CupertinoIcons.timer, size: 18),
                          const SizedBox(width: 2),
                          Text.rich(
                            TextSpan(
                              text: '${course!.course!.duration.hours} ',
                              style: TextStyle(
                                  color: Colors.grey.shade900, fontSize: 16),
                              children: [
                                TextSpan(
                                  text: "Hours ",
                                  style: TextStyle(
                                      color: Colors.grey.shade700,
                                      fontSize: 16),
                                ),
                                TextSpan(
                                  text: '${course!.course!.duration.minutes} ',
                                  style: TextStyle(
                                      color: Colors.grey.shade900,
                                      fontSize: 16),
                                ),
                                TextSpan(
                                  text: "Minutes",
                                  style: TextStyle(
                                      color: Colors.grey.shade700,
                                      fontSize: 16),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          const Icon(CupertinoIcons.play_rectangle, size: 18),
                          const SizedBox(width: 4),
                          Text.rich(
                            TextSpan(
                              text: '${course!.course!.chapters.length} ',
                              style: TextStyle(
                                  color: Colors.grey.shade900, fontSize: 16),
                              children: [
                                TextSpan(
                                  text: "Chapters ",
                                  style: TextStyle(
                                      color: Colors.grey.shade700,
                                      fontSize: 16),
                                ),
                                TextSpan(
                                  text:
                                      '${course!.course!.chapters.map((e) => e.modules.length).reduce((value, element) => value + element)} ',
                                  style: TextStyle(
                                      color: Colors.grey.shade900,
                                      fontSize: 16),
                                ),
                                TextSpan(
                                  text: "Lectures",
                                  style: TextStyle(
                                      color: Colors.grey.shade700,
                                      fontSize: 16),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text.rich(
                        TextSpan(
                          text: course!.course!.examType,
                          style: TextStyle(
                              color: Theme.of(context).primaryColor,
                              fontSize: 16),
                          children: const [
                            TextSpan(
                              text: " Exam",
                              style:
                                  TextStyle(color: Colors.black, fontSize: 16),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text.rich(
                        TextSpan(
                          text: "Purchased on: ",
                          style: TextStyle(
                              // fontWeight: FontWeight.bold,
                              color: Colors.grey.shade700,
                              fontSize: 16),
                          children: [
                            TextSpan(
                              text: course!.myCourse.startDate!
                                  .toDate()
                                  .goodDayDate(),
                              style: const TextStyle(
                                  color: Colors.black, fontSize: 16),
                            ),
                          ],
                        ),
                      ),
                      if (course!.myCourse.completedOn != null)
                        const SizedBox(height: 8),
                      if (course!.myCourse.completedOn != null)
                        Text.rich(
                          TextSpan(
                            text: "Completed on: ",
                            style: TextStyle(
                                // fontWeight: FontWeight.bold,
                                color: Colors.grey.shade700,
                                fontSize: 16),
                            children: [
                              TextSpan(
                                text: course!.myCourse.completedOn!
                                    .toDate()
                                    .goodDayDate(),
                                style: const TextStyle(
                                    color: Colors.black, fontSize: 16),
                              ),
                            ],
                          ),
                        ),
                      const SizedBox(height: 8),
                      Text(
                        "Certificate is valid for ${course!.course!.certiValidity} Days",
                        style: TextStyle(
                            // fontWeight: FontWeight.bold,
                            color: Colors.grey.shade800,
                            fontSize: 16),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        "Note: This course must be successfully finished within  ${course!.course!.days} Days from the date of ${course!.myCourse.isEnterprise ? "assigning" : "purchase"}.",
                        style: const TextStyle(
                            // fontWeight: FontWeight.bold,
                            fontStyle: FontStyle.italic),
                      ),
                      const SizedBox(height: 20),
                      if (!course!.myCourse.completed &&
                          !course!.myCourse.isEnterprise)
                        ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                          onPressed: () {
                            final ctrl = Get.find<HomeController>();
                            if (ctrl.selectedIdList.isNotEmpty) {
                              if (course!.myCourse.progress.isEmpty) {
                                ctrl.setSelectedId(ctrl.selectedIdList.first);
                              } else {
                                final tmp = course!.myCourse.progress.entries
                                    .where((element) =>
                                        element.value ==
                                        ProgressType.inProgress);
                                if (tmp.isNotEmpty) {
                                  ctrl.setSelectedId(tmp.last.key);
                                } else {
                                  ctrl.setSelectedId("Certi");
                                }
                              }
                            }
                          },
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 20.0, vertical: 12),
                            child: Text(course!.myCourse.progress.isEmpty
                                ? "Start Learning"
                                : "Continue Learning"),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ],
          );
  }
}
