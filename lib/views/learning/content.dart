import 'package:flutter/material.dart';
import 'package:wellfed/views/learning/assignment/assignment.dart';
import 'package:wellfed/views/learning/certificate/certi.dart';
import 'package:wellfed/views/learning/drawer.dart';
import 'package:wellfed/views/learning/module/module.dart';
import 'package:wellfed/views/learning/overview/overview.dart';
import '../../models/dashboard_course.dart';
import '../../utils/firebase.dart';

class LearningContent extends StatelessWidget {
  const LearningContent({
    super.key,
    required this.course,
    required this.small,
    required this.selectedId,
  });
  final DashboardCourse? course;
  final bool small;
  final String? selectedId;
  @override
  Widget build(BuildContext context) {
    return course == null
        ? Container()
        : Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (!small)
                Padding(
                  padding: const EdgeInsets.only(right: 8.0, left: 12),
                  child: LearningDrawer(
                      course: course, selectedId: selectedId, small: small),
                ),
              if (!small) const VerticalDivider(),
              Expanded(
                child: Container(
                  // color: Theme.of(context).primaryColor.withOpacity(.05),
                  child: selectedId == null
                      ? OverviewWid(course: course)
                      : getWidget(),
                ),
              ),
            ],
          );
  }

  Widget getWidget() {
    if (selectedId == "certi") {
      return CertiPage(course: course!);
    }
    for (var chap in course!.course!.chapters) {
      for (var mod in chap.modules) {
        // verifyStatus(mod.id);
        if (mod.id == selectedId) {
          return ModuleWid(
            key: Key(mod.id),
            module: mod,
            small: small,
            course: course!,
          );
        }
      }
      for (var asg in chap.assignments) {
        if (asg.id == selectedId) {
          return AssignWid(
              key: Key(asg.id), assignment: asg, small: small, course: course!);
        }
      }
    }
    return OverviewWid(course: course);
  }
}

Future<void> updateStatus(String myCourseId, Map<String, dynamic> data) async {
  try {
    debugPrint("Updating Status: $data");
    // return;
    return await FBFireStore.userCourses.doc(myCourseId).update(data);
  } catch (e) {
    debugPrint(e.toString());
  }
}
