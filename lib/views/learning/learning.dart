import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wellfed/controllers/home_ctrl.dart';
import 'package:wellfed/models/dashboard_course.dart';
import 'package:wellfed/models/user_course_model.dart';
import 'package:wellfed/utils/firebase.dart';
import 'package:wellfed/utils/loaders.dart';
import 'package:wellfed/utils/theme.dart';
import 'package:wellfed/views/learning/content.dart';
import 'package:wellfed/views/learning/drawer.dart';

import '../../utils/consts.dart';

class LearningPage extends StatefulWidget {
  const LearningPage({super.key, required this.userCourseId});
  final String userCourseId;

  @override
  State<LearningPage> createState() => _LearningPageState();
}

class _LearningPageState extends State<LearningPage> {
  bool loaded = false;
  bool listnerAdded = false;
  UserCourseModel? userCourseDoc;
  DashboardCourse? course;

  @override
  void initState() {
    super.initState();
    setUpCourseStream();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    bool small = size.width < mobileMinSize3;
    return GetBuilder<HomeController>(
      init: Get.find<HomeController>(),
      builder: (_) {
        courseMaker();
        return Scaffold(
            key: learningScafKey,
            appBar: small
                ? AppBar(
                    title: const Text("WellFed Learning"),
                  )
                : null,
            drawer: small
                ? LearningDrawer(
                    course: course,
                    small: small,
                    selectedId: _.selectedId,
                  )
                : null,
            body: loaded
                ? (course == null
                    ? _errorWid()
                    : (FBAuth.auth.currentUser?.uid != course!.myCourse.uid
                        ? _accessDenied()
                        : LearningContent(
                            course: course,
                            selectedId: _.selectedId,
                            small: small,
                          )))
                : Center(
                    child: loaderWave(
                        color: Theme.of(context).primaryColor, size: 30)));
      },
    );
  }

  // setSelectedId(String? id) => Get.find<HomeController>().setSelectedId(id);

  setUpCourseStream() async {
    try {
      FBFireStore.userCourses
          .doc(widget.userCourseId)
          .snapshots()
          .listen((event) {
        userCourseDoc = UserCourseModel.fromJson(event.id, event.data()!);
        courseMaker();
      });
    } catch (e) {
      debugPrint(e.toString());
      course = null;
      setState(() {});
    }
  }

  courseMaker() {
    if (userCourseDoc == null) return;
    final courseDoc = Get.find<HomeController>().courseList.firstWhereOrNull(
        (element) => element.docId == userCourseDoc!.courseId);
    if (courseDoc != null) {
      course = DashboardCourse(courseDoc, userCourseDoc!);
      loaded = true;
      setSelectedIdList();
    }
  }

  setSelectedIdList() {
    List<String> slist = <String>[];
    for (var chap in course!.course!.chapters) {
      for (var mod in chap.modules) {
        slist.add(mod.id);
      }
      for (var asg in chap.assignments) {
        slist.add(asg.id);
      }
    }
    Get.find<HomeController>().setSelectedIdList(slist);
  }

  Center _accessDenied() {
    return Center(
      child: Text(
        "Access Denied!",
        style: TextStyle(fontSize: 20, color: Colors.red.shade600),
      ),
    );
  }

  Center _errorWid() {
    return Center(
      child: Text(
        "Something went wrong!",
        style: appTitleTextStyle.copyWith(fontSize: 20),
      ),
    );
  }
}

/* import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wellfed/controllers/home_ctrl.dart';
import 'package:wellfed/models/course_model.dart';
import 'package:wellfed/models/dashboard_course.dart';
import 'package:wellfed/views/learning/shared/methods.dart';

class LearningDrawer extends StatelessWidget {
  const LearningDrawer(
      {super.key, required this.course, required this.selectedId});

  final DashboardCourse? course;
  final String? selectedId;

  @override
  Widget build(BuildContext context) {
    return Drawer(
      surfaceTintColor: Colors.white,
      shape: const RoundedRectangleBorder(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(20.0),
            child: ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 180),
                child: _logo()),
          ),
          _overviewTile(context),
          ...List.generate(course!.course!.chapters.length, (cIndex) {
            return ChapterTile(
                expCtrl: ExpansionTileController(),
                course: course,
                cIndex: cIndex,
                selectedId: selectedId,
                chapter: course!.course!.chapters[cIndex]);
          }),
        ],
      ),
    );
  }

  ListTile _overviewTile(BuildContext context) {
    return ListTile(
      leading: selectedId == null
          ? Icon(CupertinoIcons.doc,
              size: 20, color: Theme.of(context).primaryColor)
          : null,
      selected: selectedId == null,
      selectedColor: Theme.of(context).primaryColor,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
      onTap: () {
        learningScafKey.currentState?.closeDrawer();
        Get.find<HomeController>().setSelectedId(null);
      },
      trailing: Icon(
        Icons.arrow_forward_ios_rounded,
        size: 14,
        color: selectedId == null ? Colors.transparent : Colors.grey.shade400,
      ),
      title: const Text("Overview"),
      subtitle: Row(
        children: [
          const Icon(CupertinoIcons.timer, size: 18),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              '${course?.course?.duration.hours}h ${course?.course?.duration.minutes}m',
            ),
          ),
        ],
      ),
    );
  }
}

class ChapterTile extends StatelessWidget {
  const ChapterTile({
    super.key,
    required this.expCtrl,
    required this.course,
    required this.selectedId,
    required this.chapter,
    required this.cIndex,
  });

  final ExpansionTileController expCtrl;
  final DashboardCourse? course;
  final String? selectedId;
  final Chapters chapter;
  final int cIndex;

  @override
  Widget build(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      try {
        await Future.delayed(Duration(milliseconds: 2000));
        if (getChapterIndexFromId(course?.course, selectedId ?? "") == cIndex) {
          expCtrl.expand();
        }
      } catch (e) {
        debugPrint(e.toString());
      }
    });
    return Padding(
      padding: const EdgeInsets.all(2.0),
      child: Material(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
        child: ExpansionTile(
          controller: expCtrl,
          // initiallyExpanded:
          //     getChapterIndexFromId(course?.course, selectedId ?? "") == cIndex,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
          title: Text(chapter.name),
          subtitle: Row(
            children: [
              const Icon(CupertinoIcons.timer, size: 18),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  '${chapter.duration.hours}h ${chapter.duration.minutes}m',
                  style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                      color: Colors.grey.shade700),
                ),
              ),
            ],
          ),
          childrenPadding: const EdgeInsets.all(8),
          children: [
            ...List.generate(chapter.modules.length, (mIndex) {
              final module = chapter.modules[mIndex];
              bool selected = selectedId == module.id;
              return ListTile(
                leading: selected
                    ? Icon(CupertinoIcons.flame,
                        size: 20, color: Theme.of(context).primaryColor)
                    : null,
                selected: selected,
                selectedColor: Theme.of(context).primaryColor,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4)),
                onTap: () {
                  learningScafKey.currentState?.closeDrawer();
                  Get.find<HomeController>().setSelectedId(module.id);
                },
                trailing: Icon(
                  Icons.arrow_forward_ios_rounded,
                  size: 14,
                  color: selected ? Colors.transparent : Colors.grey.shade400,
                ),
                title: Text(module.name),
                subtitle: Row(
                  children: [
                    const Icon(CupertinoIcons.timer, size: 18),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        '${module.duration.hours}h ${module.duration.minutes}m',
                      ),
                    ),
                  ],
                ),
              );
            }),
            ...List.generate(chapter.assignments.length, (mIndex) {
              final assignment = chapter.assignments[mIndex];
              bool selected = selectedId == assignment.id;
              return ListTile(
                leading: selected
                    ? Icon(CupertinoIcons.flame,
                        size: 20, color: Theme.of(context).primaryColor)
                    : null,
                selected: selected,
                selectedColor: Theme.of(context).primaryColor,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4)),
                onTap: () {
                  learningScafKey.currentState?.closeDrawer();
                  Get.find<HomeController>().setSelectedId(assignment.id);
                },
                trailing: Icon(
                  Icons.arrow_forward_ios_rounded,
                  size: 14,
                  color: selected ? Colors.transparent : Colors.grey.shade400,
                ),
                title: Text(assignment.name),
                subtitle: Row(
                  children: [
                    const Icon(CupertinoIcons.timer, size: 18),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        '${assignment.duration.hours}h ${assignment.duration.minutes}m',
                      ),
                    ),
                  ],
                ),
              );
            })
          ],
        ),
      ),
    );
  }
}

Widget _logo() =>
    LimitedBox(maxWidth: 120, child: Image.asset('assets/logo.png'));
 */