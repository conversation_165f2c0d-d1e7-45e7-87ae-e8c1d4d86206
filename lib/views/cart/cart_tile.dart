import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wellfed/controllers/home_ctrl.dart';
import '../../models/course_model.dart';
import '../../utils/other.dart';

class CartTile extends StatelessWidget {
  const CartTile({
    super.key,
    required this.course,
    required this.qty,
    required this.isEnterprise,
  });

  final CourseModel course;
  final int qty;
  final bool isEnterprise;

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double padding = screenWidth < 350 ? 12 : 16;
    double spacing = 12;

    return Card(
      surfaceTintColor: Colors.white,
      margin: EdgeInsets.symmetric(vertical: 6, horizontal: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      elevation: 3,
      child: Padding(
        padding: EdgeInsets.all(padding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image and title row
            Row(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    course.imageUrl,
                    width: 80,
                    height: 80,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) =>
                        Image.memory(placeholderGrad, width: 80, height: 80),
                  ),
                ),
                SizedBox(width: spacing),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        course.title,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'By ${course.author}',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 14,
                        ),
                      ),
                      if (isEnterprise)
                        Container(
                          margin: const EdgeInsets.only(top: 6),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                              color: Theme.of(context).primaryColor,
                              borderRadius: BorderRadius.circular(450)),
                          child: Text(
                            "\$${course.bulkPrice} only for qty. ${course.bulkMinQty - 1}+",
                            style: const TextStyle(color: Colors.white),
                          ),
                        ),
                    ],
                  ),
                )
              ],
            ),
            SizedBox(height: spacing),
            // Duration and price row
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    const Icon(CupertinoIcons.timer, size: 18),
                    const SizedBox(width: 6),
                    Text(
                      '${course.duration.hours}h ${course.duration.minutes}m',
                      style: TextStyle(
                        color: Colors.grey.shade700,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
                Text(
                  qty >= course.bulkMinQty
                      ? '\$${course.bulkPrice}'
                      : '\$${course.price}',
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            // Quantity controls row
            if (isEnterprise)
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  IconButton(
                    onPressed: () {
                      Get.find<HomeController>()
                          .subtractQtyToCourse(course.docId);
                    },
                    icon: const Icon(CupertinoIcons.minus_square),
                    iconSize: 30,
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: Text(
                      '$qty',
                      style: const TextStyle(fontSize: 18),
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      Get.find<HomeController>().addQtyToCourse(course.docId);
                    },
                    icon: const Icon(CupertinoIcons.plus_app),
                    iconSize: 30,
                  ),
                ],
              ),
            // Remove button aligned left
            Align(
              alignment: Alignment.centerLeft,
              child: Padding(
                padding: const EdgeInsets.only(left: 24, top: 3),
                child: TextButton(
                  onPressed: () {
                    Get.find<HomeController>().removeFromCart(course.docId);
                  },
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.zero,
                    minimumSize: Size(80, 30),
                  ),
                  child: const Text(
                    "Remove",
                    style: TextStyle(color: Colors.redAccent),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _ImageBox extends StatelessWidget {
  const _ImageBox({
    required this.imageUrl,
  });

  final String imageUrl;

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(6),
      child: FadeInImage.memoryNetwork(
        placeholderErrorBuilder: (context, error, stackTrace) =>
            Image.memory(placeholderGrad),
        placeholder: placeholderGrad,
        fit: BoxFit.cover,
        image: imageUrl,
      ),
    );
  }
}
