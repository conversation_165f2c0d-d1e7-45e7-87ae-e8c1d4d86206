// ignore_for_file: no_wildcard_variable_uses

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:wellfed/controllers/home_ctrl.dart';
import 'package:wellfed/models/checkout_model.dart';
import 'package:wellfed/utils/responsive.dart';
import 'package:wellfed/utils/router.dart';
import 'package:wellfed/utils/theme.dart';
import 'cart_tile.dart';

class CartPage extends StatelessWidget {
  const CartPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const ResponsiveWid(
      mobile: CartWids(small: true),
      tablet: CartWids(small: true),
      desktop: CartWids(small: false),
    );
  }
}

class CartWids extends StatelessWidget {
  const CartWids({super.key, required this.small});
  final bool small;
  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    final padding = small
        ? const EdgeInsets.all(20)
        : EdgeInsets.symmetric(horizontal: size.width * .1, vertical: 20);
    return Padding(
      padding: padding,
      child: GetBuilder<HomeController>(
        init: Get.find<HomeController>(),
        builder: (_) {
          return _.cartList.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const _EmptyCartText(),
                      TextButton(
                          onPressed: () => context.go(Routes.courses),
                          child: const Text("Go to Courses")),
                    ],
                  ),
                )
              : SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const _TitleText(),
                      const SizedBox(height: 20),
                      Padding(
                        padding: const EdgeInsets.only(left: 6.0),
                        child: Text(
                          '${_.cartList.length} course${_.cartList.length == 1 ? "" : "s"} in cart',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                      // const Divider(),
                      const SizedBox(height: 12),
                      small
                          ? Column(
                              children: [
                                _cartItemList(_),
                                const SizedBox(height: 20),
                                const _CheckoutBox()
                              ],
                            )
                          : Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  flex: 2,
                                  child: _cartItemList(_),
                                ),
                                const SizedBox(width: 20),
                                const Expanded(
                                  child: _CheckoutBox(),
                                ),
                              ],
                            ),
                    ],
                  ),
                );
        },
      ),
    );
  }

  ListView _cartItemList(HomeController _) {
    return ListView.builder(
      itemCount: _.cartList.length,
      shrinkWrap: true,
      itemBuilder: (BuildContext context, int index) {
        final course = _.courseList.firstWhereOrNull(
            (element) => element.docId == _.cartList[index].courseId);
        return course == null
            ? const SizedBox()
            : CartTile(
                course: course,
                qty: _.cartList[index].qty,
                isEnterprise: _.isEnterprise,
              );
      },
    );
  }
}

class _CheckoutBox extends StatelessWidget {
  const _CheckoutBox();

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeController>(
      init: Get.find<HomeController>(),
      builder: (_) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(20),
            // decoration: ShapeDecoration(
            //   shadows: const [BoxShadow(color: Colors.black12, blurRadius: 6)],
            //   color: Colors.white,
            //   shape: ContinuousRectangleBorder(
            //     borderRadius: BorderRadius.circular(24),
            //   ),
            // ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Total:",
                  style: appTextStyleOne.copyWith(
                      fontWeight: FontWeight.bold, fontSize: 18),
                ),
                Text(
                  '\$${_.cartTotal ?? "N.A"}',
                  style: const TextStyle(
                      fontWeight: FontWeight.bold, fontSize: 24),
                ),
                const SizedBox(height: 18),
                Row(
                  children: [
                    Expanded(
                        child: SizedBox(
                      height: 48,
                      child: ElevatedButton.icon(
                          style: ElevatedButton.styleFrom(
                            shape: ContinuousRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            backgroundColor: Theme.of(context).primaryColor,
                          ),
                          onPressed: () {
                            if (_.cartTotal == null) return;
                            context.go(Routes.checkout,
                                extra: CheckoutModel(
                                    checkoutCourses: _.cartList,
                                    enableEdit: false));
                          },
                          icon: const Icon(
                            CupertinoIcons.creditcard,
                            color: Colors.white,
                          ),
                          label: Text(
                            "Checkout",
                            style:
                                appTextStyleTwo.copyWith(color: Colors.white),
                          )),
                    ))
                  ],
                )
              ],
            ),
          ),
        );
      },
    );
  }
}

class _TitleText extends StatelessWidget {
  const _TitleText();

  @override
  Widget build(BuildContext context) {
    return Text(
      "Shopping Cart",
      style:
          appTitleTextStyle.copyWith(fontSize: 32, fontWeight: FontWeight.bold),
    );
  }
}

class _EmptyCartText extends StatelessWidget {
  const _EmptyCartText();

  @override
  Widget build(BuildContext context) {
    return Text(
      "Your Cart is Empty",
      style:
          appTitleTextStyle.copyWith(fontSize: 32, fontWeight: FontWeight.bold),
    );
  }
}
