// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:wellfed/utils/router.dart';

class ThankYouPage extends StatelessWidget {
  const ThankYouPage({super.key});

  static const Color successGreen = Color(0xFF23A559);
  static const Color infoBlue = Color(0xFF16BFD6);
  static const Color tileBackground = Color(0xFFF8FAFC);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: LayoutBuilder(
              builder: (context, constraints) {
                double cardWidth;
                if (constraints.maxWidth < 600) {
                  // mobile
                  cardWidth = double.infinity;
                } else if (constraints.maxWidth < 980) {
                  // tablet
                  cardWidth = 500;
                } else {
                  // desktop
                  cardWidth = 500;
                }
                return Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Top green circle with check
                    Container(
                      width: 72,
                      height: 72,
                      decoration: BoxDecoration(
                        color: successGreen.withOpacity(0.08),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(Icons.check, color: successGreen, size: 44),
                    ),
                    const SizedBox(height: 24),
                    // Card Content
                    Container(
                      width: cardWidth,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 28, vertical: 30),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            blurRadius: 24,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          Text(
                            'Order Confirmed!',
                            style: TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.w700,
                              color: Colors.black87,
                            ),
                          ),
                          const SizedBox(height: 10),
                          Text(
                            'Thank you for your order. We\'re excited to help you on your food safety certification journey!',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 15,
                              color: Colors.black54,
                            ),
                          ),
                          const SizedBox(height: 26),
                          // What Happens Next
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.symmetric(
                                vertical: 16, horizontal: 18),
                            decoration: BoxDecoration(
                              color: tileBackground,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'What Happens Next?',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w600,
                                    fontSize: 15,
                                    color: Colors.black87,
                                  ),
                                ),
                                const SizedBox(height: 14),
                                // List items
                                _nextStep(
                                  icon: Icons.email_outlined,
                                  title: 'Check Your Email',
                                  subtitle:
                                      'You\'ll receive a confirmation email with your order details and access instructions within the next few minutes.',
                                ),
                                const SizedBox(height: 12),
                                _nextStep(
                                  icon: Icons.lightbulb_outline,
                                  title: 'Instant Access',
                                  subtitle:
                                      'Your training materials will be available immediately. You can start your certification course right away.',
                                ),
                                const SizedBox(height: 12),
                                _nextStep(
                                  icon: Icons.schedule_outlined,
                                  title: 'Complete at Your Pace',
                                  subtitle:
                                      'Take your time to complete the course. Your progress is automatically saved as you go.',
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 28),
                          // Buttons
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              OutlinedButton.icon(
                                onPressed: () {
                                  context.go(Routes.mycourses);
                                }, // To do: contact support
                                icon: Icon(Icons.arrow_forward_outlined,
                                    size: 18),
                                label: Text('Go to My Courses',
                                    style: TextStyle(fontSize: 15)),
                                style: OutlinedButton.styleFrom(
                                  foregroundColor: Colors.black87,
                                  side: BorderSide(color: tileBackground),
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 18, vertical: 12),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 16),
                              ElevatedButton.icon(
                                onPressed: () {
                                  context.go(Routes.home);
                                }, // To do: navigate to home
                                icon: Icon(Icons.home_outlined, size: 18),
                                label: Text('Return to Home',
                                    style: TextStyle(fontSize: 15)),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: infoBlue,
                                  foregroundColor: Colors.white,
                                  elevation: 0,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 18, vertical: 12),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 24),
                          // Help/support
                          Text(
                            'Need help? Our support team is available 24/7 to assist you.',
                            textAlign: TextAlign.center,
                            style:
                                TextStyle(fontSize: 13, color: Colors.black45),
                          ),
                          const SizedBox(height: 12),
                          // Order code
                          Text(
                            'Order confirmation #9GWCGAD3I',
                            textAlign: TextAlign.center,
                            style:
                                TextStyle(fontSize: 12, color: Colors.black38),
                          ),
                        ],
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ),
      backgroundColor: const Color(0xFFF5F6FA),
    );
  }

  Widget _nextStep(
      {required IconData icon,
      required String title,
      required String subtitle}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          margin: const EdgeInsets.only(right: 10),
          decoration: BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
            border: Border.all(color: tileBackground),
          ),
          child: Icon(icon, color: infoBlue, size: 22),
        ),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 15,
                    color: Colors.black87),
              ),
              const SizedBox(height: 3),
              Text(
                subtitle,
                style: TextStyle(fontSize: 13, color: Colors.black54),
              ),
            ],
          ),
        )
      ],
    );
  }
}
