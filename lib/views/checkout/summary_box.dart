import 'package:flutter/material.dart';
import 'package:wellfed/utils/loaders.dart';

import '../../utils/theme.dart';

class SummaryBox extends StatelessWidget {
  const SummaryBox(
      {super.key,
      required this.oroginalPrice,
      required this.discount,
      required this.loading,
      required this.small,
      required this.onCheckout,
      required this.total});
  final double oroginalPrice;
  final double discount;
  final double total;
  final bool small;
  final bool loading;
  final Function onCheckout;
  @override
  Widget build(BuildContext context) {
    return ConstrainedBox(
      constraints: small
          ? const BoxConstraints()
          : const BoxConstraints(
              maxWidth: 370,
            ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 2.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Summary",
                    style: appTextStyleOne.copyWith(
                        fontWeight: FontWeight.bold, fontSize: 18),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Original Price: ',
                        style: TextStyle(),
                      ),
                      Text(
                        '\$$oroginalPrice',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Discounts: ',
                        style: TextStyle(),
                      ),
                      Text(
                        '-\$$discount',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  const Divider(),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Total: ',
                        style: TextStyle(
                            fontWeight: FontWeight.bold, fontSize: 16),
                      ),
                      Text(
                        '\$$total',
                        style: const TextStyle(
                            fontWeight: FontWeight.bold, fontSize: 16),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 18),
            small
                ? Align(
                    alignment: Alignment.center,
                    child: _button(context),
                  )
                : Row(
                    children: [Expanded(child: _button(context))],
                  )
          ],
        ),
      ),
    );
  }

  SizedBox _button(BuildContext context) {
    return SizedBox(
      height: 48,
      child: ElevatedButton(
          style: ElevatedButton.styleFrom(
            minimumSize: const Size.fromWidth(250),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(6),
            ),
            backgroundColor: Theme.of(context).primaryColor,
          ),
          onPressed: () => onCheckout(),
          child: loading
              ? loaderWave()
              : Text(
                  "Complete Checkout",
                  style: appTextStyleTwo.copyWith(color: Colors.white),
                )),
    );
  }
}
