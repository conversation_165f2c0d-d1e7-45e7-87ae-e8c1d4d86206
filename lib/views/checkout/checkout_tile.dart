import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:wellfed/utils/theme.dart';
import '../../models/course_model.dart';
import '../../utils/other.dart';

class CheckoutTile extends StatelessWidget {
  const CheckoutTile({
    super.key,
    required this.course,
    required this.qty,
    required this.isEnterprise,
    required this.incrementQty,
    required this.enableEdit,
  });

  final CourseModel course;
  final int qty;
  final bool isEnterprise;
  final bool enableEdit;
  final Function incrementQty;

  @override
  Widget build(BuildContext context) {
    double imgSize = isEnterprise ? 60 : 40;
    return Padding(
      padding: const EdgeInsets.only(bottom: 4.0),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(2),
            child: FadeInImage.memoryNetwork(
                placeholderErrorBuilder: (context, error, stackTrace) =>
                    Image.memory(placeholderGrad),
                placeholder: placeholderGrad,
                fit: BoxFit.cover,
                height: imgSize,
                width: imgSize,
                image: course.imageUrl),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  course.title,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(
                  'By ${course.author}',
                  style: TextStyle(color: Colors.grey.shade700),
                ),
                if (isEnterprise)
                  Container(
                    color: appColorOne,
                    child: Text(
                      '\$${course.bulkPrice} for ${course.bulkMinQty - 1}+',
                      style: const TextStyle(fontSize: 12, color: Colors.white),
                    ),
                  ),
              ],
            ),
          ),
          const SizedBox(width: 12),
          Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Padding(
                padding: const EdgeInsets.only(right: 12.0),
                child: Text(
                  (isEnterprise && qty >= course.bulkMinQty)
                      ? '\$${course.bulkPrice}'
                      : '\$${course.price}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(
                    right: enableEdit && isEnterprise ? 0 : 12.0),
                child: Row(
                  children: [
                    if (enableEdit && isEnterprise)
                      IconButton(
                          onPressed: () {
                            if (qty == 1) return;
                            incrementQty(course.docId, false);
                          },
                          icon: const Icon(CupertinoIcons.minus_square)),
                    Text(
                      'x $qty',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    if (enableEdit && isEnterprise)
                      IconButton(
                          onPressed: () {
                            incrementQty(course.docId, true);
                          },
                          icon: const Icon(CupertinoIcons.plus_app)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
