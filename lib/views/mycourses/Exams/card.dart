import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:wellfed/models/user_course_model.dart';
import 'package:wellfed/utils/methods.dart';
import 'package:wellfed/views/mycourses/Exams/popups.dart';
import '../../../models/dashboard_course.dart';
import '../../../utils/other.dart';

class ExamCourseCard extends StatelessWidget {
  const ExamCourseCard({
    super.key,
    required this.course,
  });
  final DashboardCourse course;

  @override
  Widget build(BuildContext context) {
    final currentSchedule = curentSchedule();
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: AspectRatio(
          aspectRatio: 1.5 / 1,
          child: Stack(
            fit: StackFit.expand,
            children: [
              FadeInImage.memoryNetwork(
                  placeholderErrorBuilder: (context, error, stackTrace) =>
                      Image.memory(placeholderGrad),
                  placeholder: placeholderGrad,
                  fit: BoxFit.cover,
                  image: course.course!.imageUrl),
              Container(
                decoration: const BoxDecoration(
                    gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                      Colors.black12,
                      Colors.black54,
                    ])),
              ),
              Column(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Container(
                    // margin: const EdgeInsets.all(20),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: const BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.vertical(
                            top: Radius.circular(4),
                            bottom: Radius.circular(11))),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                course.course?.courseId ?? "",
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: const TextStyle(
                                    fontWeight: FontWeight.bold),
                              ),
                              Text(
                                course.course?.title ?? "",
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: const TextStyle(),
                              ),
                              const SizedBox(height: 4),
                              const Divider(height: 20),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(currentSchedule?.date
                                          .toDate()
                                          .goodDayDate() ??
                                      "Not Scheduled"),
                                  const SizedBox(width: 6),
                                  ElevatedButton.icon(
                                    onPressed: () {
                                      currentSchedule == null
                                          ? selectSchedule(context)
                                          : showInfo(context, currentSchedule);
                                    },
                                    icon: Icon(currentSchedule == null
                                        ? CupertinoIcons.calendar
                                        : CupertinoIcons.info),
                                    label: Text(currentSchedule == null
                                        ? "Schedule Now"
                                        : "Info"),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 8),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  ExamSchedule? curentSchedule() {
    try {
      DateTime? tmp;
      ExamSchedule? sch;
      if (course.myCourse.examSchedules.isEmpty) return null;
      course.myCourse.examSchedules.forEach((key, value) {
        final date = value['date'].toDate();
        if (date == null) return;
        final exS = ExamSchedule(
          id: value['id'],
          date: value['date'],
          scheduledOn: value['scheduledOn'],
          schDocId: value['schDocId'],
          courseId: value['courseId'],
          uCourseId: value['uCourseId'],
          uid: value['uid'],
          passed: value['passed'],
          canReSchedule: value['canReSchedule'],
        );
        tmp ??= date;
        sch ??= exS;
        if (date.isAfter(tmp!)) {
          tmp = date;
          sch = exS;
        }
      });
      return sch;
    } catch (e) {
      debugPrint(e.toString());
      return null;
    }
  }

  selectSchedule(BuildContext context) {
    showGeneralDialog(
      barrierDismissible: true,
      barrierLabel: "WellFed",
      context: context,
      pageBuilder: (context, animation, secondaryAnimation) {
        return ScheduleDialog(course: course);
      },
    );
  }

  showInfo(BuildContext context, ExamSchedule sch) {
    showGeneralDialog(
      barrierDismissible: true,
      barrierLabel: "WellFed",
      context: context,
      pageBuilder: (context, animation, secondaryAnimation) {
        return ExamInfoPop(sch: sch, course: course);
      },
    );
  }
}
