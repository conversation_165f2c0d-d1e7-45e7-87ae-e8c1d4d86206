import 'package:flutter/cupertino.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:wellfed/utils/other.dart';
import 'package:wellfed/utils/responsive.dart';
import '../../../controllers/home_ctrl.dart';
import '../../../models/dashboard_course.dart';
import '../../../utils/theme.dart';
import 'card.dart';

class ExamPage extends StatelessWidget {
  const ExamPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const ResponsiveWid(
      mobile: ExamCourseGrid(gridCount: 1),
      tablet: ExamCourseGrid(gridCount: 2),
      desktop: ExamCourseGrid(gridCount: 3),
    );
  }
}

class ExamCourseGrid extends StatefulWidget {
  const ExamCourseGrid({super.key, required this.gridCount});
  final int gridCount;
  @override
  State<ExamCourseGrid> createState() => _ExamCourseGridState();
}

class _ExamCourseGridState extends State<ExamCourseGrid> {
  final searchCtrl = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeController>(
      init: Get.find<HomeController>(),
      builder: (_) {
        List<DashboardCourse> myCourses = <DashboardCourse>[];
        List<DashboardCourse> filteredCourses = <DashboardCourse>[];
        for (var element in _.myCourses) {
          myCourses.add(DashboardCourse(
              _.courseList.firstWhereOrNull((e) => e.docId == element.courseId),
              element));
        }
        filteredCourses = myCourses
            .where((element) =>
                element.myCourse.completed &&
                element.course?.examType == ExamTypes.offline)
            .where((element) =>
                element.course?.title
                    .toLowerCase()
                    .contains(searchCtrl.text.toLowerCase()) ??
                false)
            .toList();
        return Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            children: [
              Row(
                children: [
                  Padding(
                    padding: const EdgeInsets.only(left: 6.0),
                    child: Text(
                      "Exam Schedules",
                      style: appTextStyleTwo.copyWith(
                          fontSize: 20, fontWeight: FontWeight.bold),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 2.0),
                    child: ConstrainedBox(
                      constraints: const BoxConstraints(maxWidth: 500),
                      child: Row(
                        children: [
                          Flexible(
                            child: CupertinoSearchTextField(
                              style: cuperSearchText,
                              placeholderStyle: cuperPlaceHolderText,
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  8, 12, 5.5, 12),
                              controller: searchCtrl,
                              onChanged: (value) =>
                                  Get.find<HomeController>().update(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(vertical: 20),
                  child: StaggeredGrid.count(
                    mainAxisSpacing: 20,
                    crossAxisSpacing: 20,
                    crossAxisCount: widget.gridCount,
                    axisDirection: AxisDirection.down,
                    children: filteredCourses
                        .map((e) => ExamCourseCard(course: e))
                        .toList(),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
