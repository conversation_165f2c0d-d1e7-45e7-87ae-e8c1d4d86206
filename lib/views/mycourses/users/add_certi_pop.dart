import 'dart:typed_data';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:wellfed/models/user_model.dart';
import 'package:wellfed/utils/consts.dart';
import 'package:wellfed/utils/firebase.dart';
import 'package:wellfed/utils/loaders.dart';
import 'package:wellfed/utils/methods.dart';

class AddCertiPopup extends StatefulWidget {
  const AddCertiPopup({super.key, required this.user});
  final UserModel user;

  @override
  State<AddCertiPopup> createState() => _AddCertiPopupState();
}

class _AddCertiPopupState extends State<AddCertiPopup> {
  bool loading = false;
  String resultTxt = "";
  Uint8List? fileBytes;
  String? fileName;
  final titleCtrl = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 400),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              "Add Certificate",
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            const Divider(),
            Text("For ${widget.user.name}"),
            const SizedBox(height: 8),
            TextField(
              controller: titleCtrl,
              decoration: _inputDecor().copyWith(hintText: "Certi Title"),
            ),
            const SizedBox(height: 20),
            InkWell(
              onTap: () => _selectFile(context),
              child: TextField(
                enabled: false,
                decoration:
                    _inputDecor().copyWith(hintText: fileName ?? "Choose file"),
              ),
            ),
            const SizedBox(height: 8),
            if (resultTxt.isNotEmpty)
              Center(
                  child: Text(
                resultTxt,
                style: const TextStyle(color: Colors.red),
              )),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                    onPressed: () => onSubmit(),
                    child: loading
                        ? loaderWave(color: Theme.of(context).primaryColor)
                        : const Text("Upload")),
              ],
            ),
          ],
        ),
      ),
    );
  }

  _selectFile(BuildContext context) async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles();
      if (result != null) {
        if (result.files.first.size / (1024 * 1024) > maxCertiMb) {
          resultTxt = "";
          debugPrint("Size Limit!!");
          resultTxt = "File size must be smaller than $maxCertiMb Mb!";
          if (context.mounted) {
            showAppSnackBar(context, "Alert",
                "File size must be smaller than $maxCertiMb Mb!");
          }
          setState(() {});
          return;
        }
        fileBytes = result.files.first.bytes;
        fileName = result.files.first.name;
        setState(() {});
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  onSubmit() async {
    try {
      if (loading || titleCtrl.text.isEmpty) return;
      setState(() => loading = true);
      // Upload file
      if (fileBytes != null) {
        final fileRef = await FBStorage.otherCertis
            .child('${widget.user.docId}/$fileName')
            .putData(fileBytes!);
        final url = await fileRef.ref.getDownloadURL();
        await FBFireStore.otherCerti.add({
          "title": titleCtrl.text,
          "addedByUid": FBAuth.auth.currentUser?.uid,
          "addedOn": FieldValue.serverTimestamp(),
          "fullname": widget.user.name,
          "url": url,
          "uid": widget.user.docId,
        });
        resultTxt = "Certificate uploaded successfully";
        if (context.mounted) {
          context.pop();
          showAppSnackBar(
              context, "Done", "Course was assigned to ${widget.user.name}");
        }
      } else {
        resultTxt = "Someting went wrong while uploading certificate!";
        if (context.mounted) {
          showAppSnackBar(context, "Ops", "Certificate was not uploaded!");
        }
      }
      setState(() => loading = false);
    } catch (e) {
      debugPrint(e.toString());
      resultTxt = "Someting went wrong while uploading certificate!";
      if (context.mounted) {
        showAppSnackBar(context, "Ops", "Certificate was not uploaded!");
      }
      setState(() => loading = false);
    }
  }

  InputDecoration _inputDecor() => InputDecoration(
        fillColor: Colors.grey.shade100,
        filled: true,
        enabledBorder: OutlineInputBorder(
          borderSide: const BorderSide(color: Colors.transparent),
          borderRadius: BorderRadius.circular(8),
        ),
      );
}
