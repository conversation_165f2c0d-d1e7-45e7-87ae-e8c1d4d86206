// ignore_for_file: use_build_context_synchronously

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:wellfed/controllers/home_ctrl.dart';
import 'package:wellfed/models/user_model.dart';
import 'package:wellfed/utils/firebase.dart';
import 'package:wellfed/utils/loaders.dart';
import 'package:wellfed/views/mycourses/users/user_popup.dart';
import '../../../utils/methods.dart';
import '../../../utils/theme.dart';
import 'add_user_popup.dart';

class EUsers extends StatelessWidget {
  const EUsers({super.key, required this.branchId});
  final String? branchId;

  @override
  Widget build(BuildContext context) {
    print(branchId);
    return StreamBuilder<List<UserModel>>(
        stream: FBFireStore.users
            .where('linkedWith.${Get.find<HomeController>().userData?.eId}',
                isEqualTo: true)
            .where('bIds.$branchId', isEqualTo: true)
            .snapshots()
            .map((event) => event.docs
                .map((e) => UserModel.fromJson(e.id, e.data()))
                .toList()),
        builder: (context, snapshot) {
          print("object : ${snapshot.data?.length}");
          if (snapshot.hasError) {
            debugPrint(snapshot.error.toString());
            return const Center(
              child: Text("Something went wrong!"),
            );
          }
          if (snapshot.hasData) {
            return EUsersWids(
              usersList: snapshot.data ?? [],
              branchId: branchId,
            );
          }
          return Center(
              child: loaderWave(color: Theme.of(context).primaryColor));
        });
  }
}

class EUsersWids extends StatefulWidget {
  const EUsersWids(
      {super.key, required this.usersList, required this.branchId});
  final List<UserModel> usersList;
  final String? branchId;
  @override
  State<EUsersWids> createState() => _EUsersWidsState();
}

class _EUsersWidsState extends State<EUsersWids> {
  final searchCtrl = TextEditingController();

  Future<void> deleteUser(UserModel user) async {
    // await FBFireStore.users.doc(user.docId).delete();
    await FBFunctions.ff.httpsCallable('deleteUser').call({'uid': user.docId});
  }

  @override
  Widget build(BuildContext context) {
    // final size = MediaQuery.sizeOf(context);
    // bool small = size.width < mobileMinSize3;
    final filteredList = widget.usersList
        .where((element) =>
            element.name
                .toLowerCase()
                .contains(searchCtrl.text.toLowerCase()) ||
            element.email.toLowerCase().contains(searchCtrl.text.toLowerCase()))
        .toList();
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12.0),
            child: Row(
              children: [
                Expanded(
                  child: Row(
                    children: [
                      // if (!small) _usersHeader(),
                      // const SizedBox(width: 12),
                      Flexible(child: _searchBox()),
                      const SizedBox(width: 12),
                    ],
                  ),
                ),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      minimumSize: const Size(100, 50),
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6))),
                  onPressed: () => _addUserPopup(),
                  child: const Text(
                    "Add User",
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ],
            ),
          ),
          filteredList.isEmpty
              ? const Center(
                  child: Padding(
                    padding: EdgeInsets.all(20.0),
                    child: Text("No linked user found!"),
                  ),
                )
              : Expanded(
                  // Expanded to fill remaining vertical space
                  child: ListView.builder(
                    padding: const EdgeInsets.only(top: 12),
                    itemCount: filteredList.length + 1, // +1 for header row
                    itemBuilder: (context, index) {
                      if (index == 0) return buildHeaderRow();
                      final user = filteredList[index - 1];
                      return SingleChildScrollView(
                        child: buildUserRow(
                          user,
                          index - 1,
                          () => _uerDetailPopup(user),
                          () => _confirmDeleteUser(user),
                          () => _uerDetailPopup(user),
                        ),
                      );
                    },
                  ),
                ),
        ],
      ),
    );
  }

  void safePop(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }
    });
  }

  Widget buildHeaderRow() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      color: Colors.grey[200],
      child: Row(
        children: const [
          Expanded(
              child: Text('Sr No.',
                  style: TextStyle(fontWeight: FontWeight.bold))),
          Expanded(
              child: Text('Name',
                  style: TextStyle(fontWeight: FontWeight.bold),
                  overflow: TextOverflow.ellipsis)),
          Expanded(
              flex: 2,
              child: Text('Email',
                  style: TextStyle(fontWeight: FontWeight.bold),
                  overflow: TextOverflow.ellipsis)),
          Expanded(
              child: Text('Number',
                  style: TextStyle(fontWeight: FontWeight.bold),
                  overflow: TextOverflow.ellipsis)),
          SizedBox(
              width: 150,
              child: Text('Actions',
                  style: TextStyle(fontWeight: FontWeight.bold))),
        ],
      ),
    );
  }

  Widget buildUserRow(UserModel user, int index, void Function() onEdit,
      void Function() onDelete, void Function() onInkwellTap) {
    return InkWell(
      onTap: onInkwellTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
        ),
        child: Row(
          children: [
            Expanded(child: Text((index + 1).toString())),
            Expanded(child: Text(user.name, overflow: TextOverflow.ellipsis)),
            Expanded(
                flex: 2,
                child: Text(user.email, overflow: TextOverflow.ellipsis)),
            Expanded(
                child:
                    Text(user.contact ?? '', overflow: TextOverflow.ellipsis)),
            SizedBox(
              width: 150,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  TextButton(
                      onPressed: () => _editUser(user),
                      child: const Icon(Icons.edit)),
                  TextButton(
                    onPressed: onDelete,
                    style: TextButton.styleFrom(foregroundColor: Colors.red),
                    child: const Icon(Icons.delete_outline_outlined),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _confirmDeleteUser(UserModel user) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return AlertDialog(
          title: const Text("Confirm Delete"),
          content: const Text("Are you sure you want to delete this user?"),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(), // Close on No
              child: const Text("No"),
            ),
            ElevatedButton(
              onPressed: () async {
                final res = await _deleteUser(user); // Call delete logic
                if (res) {
                  Navigator.of(context).pop(); // Close dialog
                }
                // Navigator.of(context).pop(); // Close dialog
                // context.pop();
              },
              child: const Text("Yes"),
            ),
          ],
        );
      },
    );
  }

  void showLoader(BuildContext context, [String? message]) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => WillPopScope(
        onWillPop: () async => false,
        child: AlertDialog(
          content: Row(
            children: [
              const CircularProgressIndicator(),
              const SizedBox(width: 20),
              Text(message ?? "Loading..."),
            ],
          ),
        ),
      ),
    );
  }

  Future<bool> _deleteUser(UserModel user) async {
    try {
      // showLoader(context, "Deleting user...");
      await deleteUser(user);

      showAppSnackBar(context, "Success", "User deleted successfully");
      return true;
    } catch (e) {
      if (!mounted) return false;
//      Navigator.of(context).pop(); // Close loader dialog if error
      showAppSnackBar(context, "Error", "Failed to delete user: $e");
      return false;
    }
  }

  void _editUser(UserModel user) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) {
        return Dialog(
            child: AddUserDialog(
          userImportNoti: userImportNoti,
          bId: widget.branchId,
          user: user, // Pass the existing user here for editing
        ));
      },
    );
  }

  void _uerDetailPopup(UserModel user) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) {
        return Dialog(
            backgroundColor: Colors.white, child: UserDetailPopup(user: user));
      },
    );
  }

  void _addUserPopup() {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) {
        return Dialog(
            child: AddUserDialog(
                userImportNoti: userImportNoti, bId: widget.branchId));
      },
    );
  }

  void userImportNoti() {
    if (!context.mounted) return;
    showAppSnackBar(context, "Done", "User was imported successfully");
  }

  Padding _searchBox() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 2.0),
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 500),
        child: Row(
          children: [
            Expanded(
              child: CupertinoSearchTextField(
                placeholderStyle: cuperPlaceHolderText,
                style: cuperSearchText,
                padding: const EdgeInsetsDirectional.fromSTEB(8, 12, 5.5, 12),
                controller: searchCtrl,
                placeholder: "Search",
                onChanged: (value) => setState(() {}),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
// class Small extends StatelessWidget {
//   const Small({super.key});
//   final TextEditingController searchCtrl;

//   @override
//   Widget build(BuildContext context) {
//     return const Placeholder();
//   }
// }
