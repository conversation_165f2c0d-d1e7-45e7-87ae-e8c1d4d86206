import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:wellfed/controllers/home_ctrl.dart';
import 'package:wellfed/models/dashboard_course.dart';
import 'package:wellfed/models/other_certi_model.dart';
import 'package:wellfed/models/user_course_model.dart';
import 'package:wellfed/models/user_model.dart';
import 'package:wellfed/utils/methods.dart';
import 'package:wellfed/views/mycourses/users/assign_crse_popup.dart';
import '../../../utils/firebase.dart';
import '../../../utils/loaders.dart';
import '../../../utils/router.dart';
import '../dashboard/courses_box.dart';
import 'add_certi_pop.dart';

class UserDetailPopup extends StatefulWidget {
  const UserDetailPopup({super.key, required this.user});
  final UserModel user;

  @override
  State<UserDetailPopup> createState() => _UserDetailPopupState();
}

class _UserDetailPopupState extends State<UserDetailPopup> {
  // Use an enum or int to track selection (0: Courses, 1: Other Certi)
  int segmentedValue = 0;

  @override
  Widget build(BuildContext context) {
    return ConstrainedBox(
      constraints: const BoxConstraints(
        maxWidth: 600,
        maxHeight: 700,
      ),
      child: Material(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        clipBehavior: Clip.antiAlias,
        child: Stack(
          children: [
            Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // User info row without close button now
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(widget.user.name,
                                style: Theme.of(context)
                                    .textTheme
                                    .titleLarge
                                    ?.copyWith(fontWeight: FontWeight.bold)),
                            const SizedBox(height: 4),
                            Text(widget.user.email,
                                style: TextStyle(
                                    color: Colors.grey.shade700, fontSize: 14)),
                            Text(widget.user.contact ?? '',
                                style: TextStyle(color: Colors.grey.shade700)),
                          ],
                        ),
                      ),
                      // remove close icon from here
                    ],
                  ),

                  const SizedBox(height: 12),

                  // The segmented control and the rest of the content
                  CupertinoSlidingSegmentedControl<int>(
                    groupValue: segmentedValue,
                    children: const <int, Widget>{
                      0: Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: 16.0, vertical: 10.0),
                        child: Text('Courses'),
                      ),
                      1: Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: 16.0, vertical: 10.0),
                        child: Text('Other Certificates'),
                      ),
                    },
                    onValueChanged: (int? val) {
                      if (val != null) {
                        setState(() {
                          segmentedValue = val;
                        });
                      }
                    },
                  ),

                  const SizedBox(height: 18),

                  Expanded(
                    child: segmentedValue == 0
                        ? StreamBuilder<List<UserCourseModel>>(
                            stream: FBFireStore.userCourses
                                .where('uid', isEqualTo: widget.user.docId)
                                .snapshots()
                                .map((event) => event.docs
                                    .map((e) => UserCourseModel.fromJson(
                                        e.id, e.data()))
                                    .toList()),
                            builder: (context, snapshot) {
                              if (snapshot.hasError) {
                                debugPrint(snapshot.error.toString());
                                return const Center(
                                    child: Text("Something went wrong!"));
                              }
                              if (!snapshot.hasData) {
                                return Center(
                                    child: loaderWave(
                                        color: Theme.of(context).primaryColor));
                              }
                              return EUserCoursesWids(crseList: snapshot.data!);
                            },
                          )
                        : StreamBuilder<List<OtherCertiModel>>(
                            stream: FBFireStore.otherCerti
                                .where('uid', isEqualTo: widget.user.docId)
                                .snapshots()
                                .map((event) =>
                                    OtherCertiModel.toCertiList(event)
                                        .toList()),
                            builder: (context, snapshot) {
                              if (snapshot.hasError) {
                                debugPrint(snapshot.error.toString());
                                return const Center(
                                    child: Text("Something went wrong!"));
                              }
                              if (!snapshot.hasData) {
                                return Center(
                                    child: loaderWave(
                                        color: Theme.of(context).primaryColor));
                              }
                              return EUserCertiWid(certiList: snapshot.data!);
                            },
                          ),
                  ),
                ],
              ),
            ),

            // Close button positioned top-right over the content
            Positioned(
              top: 8,
              right: 8,
              child: IconButton(
                icon: const Icon(Icons.close),
                tooltip: 'Close',
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _assignCoursePop(UserModel user) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) {
        return Dialog(child: AssignCoursePopup(user: user));
      },
    );
  }

  void _addCertiPop(UserModel user) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) {
        return Dialog(child: AddCertiPopup(user: user));
      },
    );
  }
}

class EUserCoursesWids extends StatefulWidget {
  const EUserCoursesWids({super.key, required this.crseList});
  final List<UserCourseModel> crseList;
  @override
  State<EUserCoursesWids> createState() => _EUserCoursesWidsState();
}

class _EUserCoursesWidsState extends State<EUserCoursesWids> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        widget.crseList.isEmpty
            ? const Center(
                child: Padding(
                  padding: EdgeInsets.all(20.0),
                  child: Text("No course assigned yet!"),
                ),
              )
            : SingleChildScrollView(
                // padding: const EdgeInsets.all(12),
                child: ListView.builder(
                  padding: const EdgeInsets.only(top: 12),
                  itemCount: widget.crseList.length,
                  shrinkWrap: true,
                  itemBuilder: (BuildContext context, int index) {
                    final uCrse = widget.crseList[index];
                    final crse = Get.find<HomeController>()
                        .courseList
                        .firstWhereOrNull((element) =>
                            element.docId == widget.crseList[index].courseId);
                    final expiresIn = uCrse.endDate!
                        .toDate()
                        .difference(DateTime.now())
                        .inDays;
                    final percentage =
                        getPercentage(DashboardCourse(crse, uCrse));
                    return Card(
                      clipBehavior: Clip.hardEdge,
                      child: ExpansionTile(
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(6)),
                        title: Text(crse?.title ?? ""),
                        subtitle: Text.rich(
                          TextSpan(text: "Status: ", children: [
                            TextSpan(
                              text: uCrse.completed
                                  ? " Completed "
                                  : expiresIn >= 0
                                      ? "Expires in $expiresIn days"
                                      : " Expired ",
                              style: TextStyle(
                                  color: uCrse.completed
                                      ? Colors.green
                                      : Colors.red),
                            )
                          ]),
                        ),
                        trailing: uCrse.completed
                            ? IconButton(
                                tooltip: uCrse.certiUrl == null
                                    ? "Waiting for Upload"
                                    : "View Certi",
                                onPressed: () => uCrse.certiUrl != null
                                    ? context
                                        .go('${Routes.certi}/${uCrse.certiUrl}')
                                    : null,
                                icon: uCrse.certiUrl == null
                                    ? const Icon(CupertinoIcons.hourglass)
                                    : const Icon(CupertinoIcons.doc_append))
                            : CircularPercentIndicator(
                                radius: 20.0,
                                lineWidth: 5.0,
                                percent: percentage,
                                // percent: 0.60,
                                // restartAnimation: true,
                                animationDuration: 1000,
                                animation: true,
                                center: Text(
                                  "${percentage * 100}%",
                                  style: const TextStyle(fontSize: 12),
                                ),
                                progressColor: Theme.of(context).primaryColor,
                              ),
                        expandedCrossAxisAlignment: CrossAxisAlignment.start,
                        expandedAlignment: Alignment.centerLeft,
                        childrenPadding: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 12),
                        children: [
                          Text.rich(
                            TextSpan(
                                text: "Exam Type: ",
                                style: TextStyle(color: Colors.grey.shade700),
                                children: [
                                  TextSpan(
                                    text: crse?.examType,
                                    style: const TextStyle(color: Colors.black),
                                  )
                                ]),
                          ),
                          Text.rich(
                            TextSpan(
                                text: "Assigned On: ",
                                style: TextStyle(color: Colors.grey.shade700),
                                children: [
                                  TextSpan(
                                    text:
                                        uCrse.startDate?.toDate().goodDayDate(),
                                    style: const TextStyle(color: Colors.black),
                                  )
                                ]),
                          ),
                          if (uCrse.completed)
                            Text.rich(
                              TextSpan(
                                  text: "Completed On: ",
                                  style: TextStyle(color: Colors.grey.shade700),
                                  children: [
                                    TextSpan(
                                      text: uCrse.completedOn
                                          ?.toDate()
                                          .goodDayDate(),
                                      style:
                                          const TextStyle(color: Colors.black),
                                    )
                                  ]),
                            ),
                        ],
                      ),
                    );
                  },
                ),
              ),
      ],
    );
  }
}

class EUserCertiWid extends StatefulWidget {
  const EUserCertiWid({super.key, required this.certiList});
  final List<OtherCertiModel> certiList;
  @override
  State<EUserCertiWid> createState() => _EUserCertiWidState();
}

class _EUserCertiWidState extends State<EUserCertiWid> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        widget.certiList.isEmpty
            ? const Center(
                child: Padding(
                  padding: EdgeInsets.all(20.0),
                  child: Text("No certifiate added yet!"),
                ),
              )
            : SingleChildScrollView(
                // padding: const EdgeInsets.all(12),
                child: ListView.builder(
                  padding: const EdgeInsets.only(top: 12),
                  itemCount: widget.certiList.length,
                  shrinkWrap: true,
                  itemBuilder: (BuildContext context, int index) {
                    final certi = widget.certiList[index];
                    return Card(
                      clipBehavior: Clip.hardEdge,
                      child: ListTile(
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(6)),
                        title: Text(certi.title),
                        subtitle: Text.rich(
                          TextSpan(
                              text: "Added On: ",
                              style: TextStyle(color: Colors.grey.shade700),
                              children: [
                                TextSpan(
                                  text: certi.addedOn.goodDayDate(),
                                  style: const TextStyle(color: Colors.black),
                                )
                              ]),
                        ),
                        trailing: IconButton(
                            onPressed: () async {
                              try {
                                final url = Uri.parse(certi.url);
                                if (await canLaunchUrl(url)) {
                                  launchUrl(url,
                                      mode: LaunchMode.externalApplication);
                                }
                              } catch (e) {
                                debugPrint(e.toString());
                              }
                            },
                            icon: const Icon(CupertinoIcons.cloud_download)),
                      ),
                    );
                  },
                ),
              ),
      ],
    );
  }
}
