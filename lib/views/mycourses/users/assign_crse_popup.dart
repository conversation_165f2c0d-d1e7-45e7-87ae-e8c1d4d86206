import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:wellfed/controllers/home_ctrl.dart';
import 'package:wellfed/models/dashboard_course.dart';
import 'package:wellfed/models/user_model.dart';
import 'package:wellfed/utils/firebase.dart';
import 'package:wellfed/utils/loaders.dart';
import 'package:wellfed/utils/methods.dart';

class AssignCoursePopup extends StatefulWidget {
  const AssignCoursePopup({super.key, required this.user});
  final UserModel user;

  @override
  State<AssignCoursePopup> createState() => _AssignCoursePopupState();
}

class _AssignCoursePopupState extends State<AssignCoursePopup> {
  String? selected;
  bool loading = false;
  RxString resultTxt = "".obs;

  @override
  Widget build(BuildContext context) {
    final ctrl = Get.find<HomeController>();
    final crseList = ctrl.myCourses.where((element) => element.qty > 0).map(
        (e) => DashboardCourse(
            ctrl.courseList
                .firstWhereOrNull((element) => element.docId == e.courseId),
            e));
    return Padding(
      padding: const EdgeInsets.all(20),
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 400),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              "Assign Course",
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            const Divider(),
            Text("Assigning to ${widget.user.name}"),
            const SizedBox(height: 8),
            DropdownButtonFormField(
              value: selected,
              isExpanded: true,
              hint: const Text("Select course"),
              decoration: InputDecoration(
                  filled: true,
                  fillColor: Colors.grey.shade200,
                  enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(6),
                      borderSide: BorderSide.none)),
              items: crseList
                  .map((e) => DropdownMenuItem(
                        value: e.myCourse.docId,
                        child: SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Text(
                            e.course?.title ?? "",
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ))
                  .toList(),
              onChanged: (value) => setState(() => selected = value),
            ),
            const SizedBox(height: 8),
            if (resultTxt.isNotEmpty) Text(resultTxt.value),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                    onPressed: () => onSubmit(crseList.firstWhere(
                        (element) => element.myCourse.docId == selected)),
                    child: loading
                        ? loaderWave(color: Theme.of(context).primaryColor)
                        : const Text("Confirm")),
              ],
            ),
          ],
        ),
      ),
    );
  }

  onSubmit(DashboardCourse dashCrse) async {
    try {
      if (dashCrse.course == null || loading) return;
      setState(() => loading = true);
      final result =
          await FBFireStore.fs.runTransaction<bool>((transaction) async {
        final myCourseDoc = await transaction
            .get(FBFireStore.userCourses.doc(dashCrse.myCourse.docId));
        if (myCourseDoc.exists) {
          final left = myCourseDoc.get('qty') - myCourseDoc.get('assigned');
          if (left > 0) {
            // Creating User Course
            transaction.set(FBFireStore.userCourses.doc(), {
              "courseId": dashCrse.course!.docId,
              "isEnterprise": false,
              "uid": widget.user.docId,
              "orderId": null,
              "assignedByUid": FBAuth.auth.currentUser?.uid,
              "assignedByName": Get.find<HomeController>().userData?.name,
              "blocked": false,
              "startDate": FieldValue.serverTimestamp(),
              "endDate":
                  DateTime.now().add(Duration(days: dashCrse.course!.days)),
              "examSchedules": {},
              "certiUrl": null,
              "score": null,
              "progress": {},
              "asignScores": {},
              "qty": 1,
              "assigned": 0,
              "completed": false,
              "time": FieldValue.serverTimestamp(),
            });
            // Adding Assigned Count
            transaction.update(
                myCourseDoc.reference, {"assigned": FieldValue.increment(1)});
            return true;
          }
          return false;
        }
        return false;
      });
      if (result) {
        resultTxt.value = "Course assigned successfully";
        if (context.mounted) {
          context.pop();
          showAppSnackBar(
              context, "Done", "Course was assigned to ${widget.user.name}");
        }
      } else {
        resultTxt.value = "Someting went wrong while assigning course!";
        if (context.mounted) {
          showAppSnackBar(context, "Ops", "Course was not assigned!");
        }
      }
      setState(() => loading = false);
    } catch (e) {
      debugPrint(e.toString());
      setState(() => loading = false);
    }
  }
}
