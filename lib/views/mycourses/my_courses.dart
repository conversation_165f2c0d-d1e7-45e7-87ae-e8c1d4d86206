import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:wellfed/utils/router.dart';
import '../../controllers/home_ctrl.dart';
import '../../models/e_branch_model.dart';
import '../../utils/consts.dart';
import '../../utils/firebase.dart';
import 'drawer.dart';

class MyCourses extends StatefulWidget {
  const MyCourses({super.key, required this.child});
  final StatefulNavigationShell child;

  @override
  State<MyCourses> createState() => _MyCoursesState();
}

class _MyCoursesState extends State<MyCourses> {
  bool branchesLoaded = false;

  @override
  void initState() {
    super.initState();
  }

  Future<void> initBranches() async {
    if (branchesLoaded) return;
    branchesLoaded = true;
    FBFireStore.eBranches
        .where('uId', isEqualTo: FBAuth.auth.currentUser?.uid)
        .snapshots()
        .listen((event) {
      Get.find<HomeController>().branches = event.docs
          .map((e) => EBranch(
              docId: e.id, bId: e['bId'], name: e['name'], uId: e['uId']))
          .toList();
      Get.find<HomeController>().update();
    });
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    bool small = size.width < mobileMinSize3;
    return GetBuilder<HomeController>(
      init: Get.find<HomeController>(),
      builder: (_) {
        if (_.isEnterprise) initBranches();
        return Scaffold(
          key: myCoursesScafKey,
          appBar: small ? _appBar() : null,
          drawer: small ? DashboardDrawer(isEnterprise: _.isEnterprise) : null,
          body: Row(
            children: [
              if (!small)
                Container(
                  width: size.width * .2,
                  constraints: const BoxConstraints(minWidth: 300),
                  child: DashDrawerWids(isEnterprise: _.isEnterprise),
                ),
              Expanded(flex: 7, child: widget.child),
            ],
          ),
        );
      },
    );
  }

  AppBar _appBar() => AppBar(
        title: Text(_getTitle()),
      );

  String _getTitle() {
    final currentLocation = GoRouterState.of(context).uri.toString();
    if (currentLocation.contains(Routes.users)) {
      return "Users";
    }
    switch (currentLocation) {
      case Routes.mycourses:
        return "Dashboard";
      case Routes.exams:
        return "Exam Scheduled";
      case Routes.certificates:
        return "Certificates";
      case Routes.offline:
        return "QR (Offline Exam)";
      case Routes.users:
        return "Users";
      default:
        return "";
    }
  }
}
