import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'package:wellfed/models/user_course_model.dart';
import 'package:wellfed/utils/router.dart';
import '../../../controllers/home_ctrl.dart';
import '../../../models/dashboard_course.dart';
import '../../../utils/other.dart';
import '../../../utils/theme.dart';

class CoursesContainer extends StatelessWidget {
  const CoursesContainer({
    super.key,
    required this.myCourseSearch,
    required this.filteredCourses,
    required this.expanded,
    required this.gridCount,
  });

  final TextEditingController myCourseSearch;
  final List<DashboardCourse> filteredCourses;
  final bool expanded;
  final int gridCount;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 6.0),
            child: Text(
              "My Courses",
              style: appTextStyleTwo.copyWith(
                  fontSize: 20, fontWeight: FontWeight.bold),
            ),
          ),
          const SizedBox(height: 12),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 2.0),
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 500),
              child: Row(
                children: [
                  Flexible(
                    child: CupertinoSearchTextField(
                      style: cuperSearchText,
                      placeholderStyle: cuperPlaceHolderText,
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(8, 12, 5.5, 12),
                      controller: myCourseSearch,
                      onChanged: (value) => Get.find<HomeController>().update(),
                    ),
                  ),
                ],
              ),
            ),
          ),
          expanded
              ? Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(vertical: 20),
                    child: _grid(context),
                  ),
                )
              : Padding(
                  padding: const EdgeInsets.symmetric(vertical: 20),
                  child: _grid(context),
                ),
        ],
      ),
    );
  }

  StaggeredGrid _grid(BuildContext context) {
    final isEnterprise = Get.find<HomeController>().isEnterprise;
    return StaggeredGrid.count(
        mainAxisSpacing: 20,
        crossAxisSpacing: 20,
        axisDirection: AxisDirection.down,
        crossAxisCount: gridCount,
        children: filteredCourses.map((e) {
          final expiresIn =
              e.myCourse.endDate!.toDate().difference(DateTime.now()).inDays;
          final percentage = getPercentage(e);
          return InkWell(
            hoverColor: Colors.transparent,
            borderRadius: BorderRadius.circular(15),
            onTap: () {
              Get.find<HomeController>().selectedId = null;
              context.go('${Routes.learn}/${e.myCourse.docId}');
            },
            child: Card(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: AspectRatio(
                    aspectRatio: 1.5 / 1,
                    child: Stack(
                      fit: StackFit.expand,
                      children: [
                        FadeInImage.memoryNetwork(
                            placeholderErrorBuilder:
                                (context, error, stackTrace) =>
                                    Image.memory(placeholderGrad),
                            placeholder: placeholderGrad,
                            fit: BoxFit.cover,
                            image: e.course!.imageUrl),
                        Container(
                          decoration: const BoxDecoration(
                              gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                Colors.black12,
                                Colors.black54,
                              ])),
                        ),
                        Column(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Container(
                              // margin: const EdgeInsets.all(20),
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 8),
                              decoration: const BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.vertical(
                                      top: Radius.circular(4),
                                      bottom: Radius.circular(11))),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          e.course?.courseId ?? "",
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                          style: const TextStyle(
                                              fontWeight: FontWeight.bold),
                                        ),
                                        Text(
                                          e.course?.title ?? "",
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                          style: const TextStyle(),
                                        ),
                                        const SizedBox(height: 4),
                                        isEnterprise
                                            ? Container(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 8),
                                                decoration: BoxDecoration(
                                                    color: appColorOne
                                                        .withOpacity(.2),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            6)),
                                                child: Row(
                                                  mainAxisSize:
                                                      MainAxisSize.min,
                                                  children: [
                                                    Text.rich(TextSpan(
                                                        text: "Total: ",
                                                        children: [
                                                          TextSpan(
                                                              text:
                                                                  '${e.myCourse.qty}',
                                                              style: const TextStyle(
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold))
                                                        ])),
                                                    const SizedBox(width: 12),
                                                    Text.rich(TextSpan(
                                                        text: "Remaining: ",
                                                        children: [
                                                          TextSpan(
                                                              text:
                                                                  '${e.myCourse.qty - e.myCourse.assigned}',
                                                              style: const TextStyle(
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold))
                                                        ]))
                                                  ],
                                                ),
                                              )
                                            : Container(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 8),
                                                decoration: BoxDecoration(
                                                    color: e.myCourse.completed
                                                        ? Colors.green.shade50
                                                        : Colors.red.shade50,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            12)),
                                                child: Text(
                                                  e.myCourse.completed
                                                      ? " Completed "
                                                      : expiresIn >= 0
                                                          ? "Expires in $expiresIn days"
                                                          : " Expired ",
                                                  style: TextStyle(
                                                      color:
                                                          e.myCourse.completed
                                                              ? Colors.green
                                                              : Colors.red),
                                                ))
                                      ],
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  e.myCourse.completed || isEnterprise
                                      ? const SizedBox()
                                      : CircularPercentIndicator(
                                          radius: 20.0,
                                          lineWidth: 5.0,
                                          percent: percentage,
                                          // percent: 0.60,
                                          // restartAnimation: true,
                                          animationDuration: 1000,
                                          animation: true,
                                          center: Text(
                                            "${percentage * 100}%",
                                            style:
                                                const TextStyle(fontSize: 12),
                                          ),
                                          progressColor:
                                              Theme.of(context).primaryColor,
                                        ),
                                ],
                              ),
                            ),
                          ],
                        )
                      ],
                    ),
                  )),
            ),
          );
        }).toList());
  }
}

double getPercentage(DashboardCourse course) {
  final totalLessons = course.course?.chapters
          .map((e) => e.modules.length + e.assignments.length)
          .toList()
          .reduce((value, element) => value + element) ??
      0;
  int completedLessons = 0;
  course.myCourse.progress.forEach((key, value) {
    if (value == ProgressType.completed) {
      completedLessons++;
    }
  });
  return double.parse((completedLessons / totalLessons).toStringAsFixed(2));
}
