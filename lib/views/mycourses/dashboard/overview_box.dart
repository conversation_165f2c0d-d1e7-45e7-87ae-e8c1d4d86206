import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import '../../../models/dashboard_course.dart';
import '../../../utils/theme.dart';

const cardColors = [Colors.amber, Colors.blue, Colors.deepPurple];

class OverviewBox extends StatelessWidget {
  const OverviewBox({
    super.key,
    required this.corses,
    required this.vertical,
    required this.limited,
    required this.isEnterprise,
  });
  final List<DashboardCourse> corses;
  final bool vertical;
  final bool limited;
  final bool isEnterprise;
  @override
  Widget build(BuildContext context) {
    final crseCount = getCourseQtyCount();
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
          color: (vertical && !limited)
              ? Theme.of(context).primaryColor.withOpacity(.08)
              : null,
          borderRadius: BorderRadius.circular(24)),
      child: Padding(
        padding: EdgeInsets.all(vertical && !limited ? 20.0 : 0),
        child: vertical
            ? Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (!limited) _overviewText(),
                  const SizedBox(height: 20),
                  Flexible(child: _cardOne(crseCount)),
                  const SizedBox(height: 20),
                  Flexible(child: _cardTwo(crseCount)),
                  const SizedBox(height: 20),
                  // Expanded(child: Container()),
                  Flexible(child: _cardThree(crseCount)),
                  const SizedBox(height: 20),
                ],
              )
            : LimitedBox(
                // maxHeight: 300,
                child: Column(
                  children: [
                    // _overviewText(),
                    // const SizedBox(height: 12),
                    Row(
                      children: [
                        const SizedBox(height: 20),
                        Expanded(child: _cardOne(crseCount)),
                        const SizedBox(height: 20),
                        Expanded(child: _cardTwo(crseCount)),
                        const SizedBox(height: 20),
                        // Expanded(child: Container()),
                        Expanded(child: _cardThree(crseCount)),
                        const SizedBox(height: 20),
                      ],
                    )
                  ],
                ),
              ),
      ),
    );
  }

  Widget _cardThree(CourseQtyCount cQC) {
    return LimitedBox(
      maxHeight: 150,
      child: Card(
        surfaceTintColor: Colors.white,
        margin: const EdgeInsets.symmetric(horizontal: 20),
        shape: ContinuousRectangleBorder(
          borderRadius: BorderRadius.circular(45),
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Container(
                      padding: const EdgeInsets.all(2),
                      decoration: ShapeDecoration(
                        color: cardColors[2].shade400,
                        shape: ContinuousRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Icon(
                        CupertinoIcons.doc_text_viewfinder,
                        color: Colors.white,
                        size: 18,
                      )),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      isEnterprise
                          ? "Unassigned Courses"
                          : "Certificates Earned",
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style:
                          appTextStyleTwo.copyWith(fontWeight: FontWeight.bold),
                    ),
                  ),
                ],
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: FittedBox(
                        fit: BoxFit.contain,
                        child: Padding(
                          padding: const EdgeInsets.only(top: 8.0),
                          child: Text(
                            isEnterprise
                                ? (cQC.total - cQC.assigned)
                                    .toString()
                                    .padLeft(2, "0")
                                : corses
                                    .where((element) =>
                                        element.myCourse.certiUrl != null)
                                    .length
                                    .toString()
                                    .padLeft(2, "0"),
                            style: appTextStyleTwo.copyWith(
                                fontWeight: FontWeight.bold, fontSize: 50),
                          ),
                        ),
                      ),
                    ),
                    Container(
                      height: 5,
                      width: 60,
                      decoration: BoxDecoration(
                          gradient: LinearGradient(colors: [
                            cardColors[2].shade300,
                            cardColors[2].shade400,
                          ]),
                          borderRadius: BorderRadius.circular(2)),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _cardTwo(CourseQtyCount cQC) {
    return LimitedBox(
      maxHeight: 150,
      child: Card(
        surfaceTintColor: Colors.white,
        margin: const EdgeInsets.symmetric(horizontal: 20),
        shape: ContinuousRectangleBorder(
          borderRadius: BorderRadius.circular(45),
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Container(
                      padding: const EdgeInsets.all(2),
                      decoration: ShapeDecoration(
                        color: cardColors[1].shade400,
                        shape: ContinuousRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Icon(
                        CupertinoIcons.checkmark_seal,
                        color: Colors.white,
                        size: 18,
                      )),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      isEnterprise ? "Courses Assigned" : "Courses Completed",
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style:
                          appTextStyleTwo.copyWith(fontWeight: FontWeight.bold),
                    ),
                  ),
                ],
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: FittedBox(
                        fit: BoxFit.contain,
                        child: Padding(
                          padding: const EdgeInsets.only(top: 8.0),
                          child: Text(
                            isEnterprise
                                ? cQC.assigned.toString().padLeft(2, "0")
                                : corses
                                    .where(
                                        (element) => element.myCourse.completed)
                                    .length
                                    .toString()
                                    .padLeft(2, "0"),
                            style: appTextStyleTwo.copyWith(
                                fontWeight: FontWeight.bold, fontSize: 50),
                          ),
                        ),
                      ),
                    ),
                    Container(
                      height: 5,
                      width: 60,
                      decoration: BoxDecoration(
                          gradient: LinearGradient(colors: [
                            cardColors[1].shade300,
                            cardColors[1].shade400,
                          ]),
                          borderRadius: BorderRadius.circular(2)),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _cardOne(CourseQtyCount cQC) {
    return LimitedBox(
      maxHeight: 150,
      child: Card(
        surfaceTintColor: Colors.white,
        margin: const EdgeInsets.symmetric(horizontal: 20),
        shape: ContinuousRectangleBorder(
          borderRadius: BorderRadius.circular(45),
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Container(
                      padding: const EdgeInsets.all(2),
                      decoration: ShapeDecoration(
                        color: cardColors[0].shade400,
                        shape: ContinuousRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Icon(
                        CupertinoIcons.graph_circle,
                        color: Colors.white,
                        size: 18,
                      )),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      isEnterprise ? "Total Courses" : "Courses in Progress",
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: appTextStyleTwo.copyWith(
                          fontWeight: FontWeight.bold, fontSize: 16),
                    ),
                  ),
                  Container()
                ],
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: FittedBox(
                        fit: BoxFit.contain,
                        child: Padding(
                          padding: const EdgeInsets.only(top: 8.0),
                          child: Text(
                            isEnterprise
                                ? cQC.total.toString().padLeft(2, "0")
                                : corses
                                    .where((element) =>
                                        !element.myCourse.completed)
                                    .length
                                    .toString()
                                    .padLeft(2, "0"),
                            style: appTextStyleTwo.copyWith(
                                fontWeight: FontWeight.bold, fontSize: 50),
                          ),
                        ),
                      ),
                    ),
                    Container(
                      height: 5,
                      width: 60,
                      decoration: BoxDecoration(
                          gradient: LinearGradient(colors: [
                            cardColors[0].shade300,
                            cardColors[0].shade400,
                          ]),
                          borderRadius: BorderRadius.circular(2)),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Align _overviewText() {
    return Align(
      alignment: Alignment.centerLeft,
      child: Padding(
        padding: const EdgeInsets.only(left: 28.0, top: 8),
        child: Text(
          "Overview",
          style: appTextStyleTwo.copyWith(
              fontSize: 18, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }

  CourseQtyCount getCourseQtyCount() {
    int total = 0;
    int assigned = 0;
    for (var element in corses) {
      total += element.myCourse.qty;
      assigned += element.myCourse.assigned;
    }
    return CourseQtyCount(total, assigned);
  }
}

class CourseQtyCount {
  final int total;
  final int assigned;

  CourseQtyCount(this.total, this.assigned);
}
