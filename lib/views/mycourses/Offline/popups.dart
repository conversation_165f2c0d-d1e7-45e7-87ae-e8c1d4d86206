import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:wellfed/controllers/home_ctrl.dart';
import 'package:wellfed/models/user_course_model.dart';
import 'package:wellfed/utils/methods.dart';
import 'package:wellfed/utils/theme.dart';
import '../../../models/dashboard_course.dart';
import '../../../models/schedule_model.dart';
import '../../../services/docs/gen.dart';
import '../../../utils/firebase.dart';
import '../../../utils/loaders.dart';

class ScheduleDialog extends StatefulWidget {
  const ScheduleDialog({
    super.key,
    required this.course,
  });
  final DashboardCourse course;

  @override
  State<ScheduleDialog> createState() => _ScheduleDialogState();
}

class _ScheduleDialogState extends State<ScheduleDialog> {
  ScheduleModel? selectedDoc;
  bool loading = false;
  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: StreamBuilder<List<ScheduleModel>>(
          stream: FBFireStore.schedules
              .where('courseId', isEqualTo: widget.course.course?.docId)
              .where('date', isGreaterThan: DateTime.now())
              .snapshots()
              .map(ScheduleModel.toSchList),
          builder: (context, snapshot) {
            if (snapshot.hasError) {
              debugPrint(snapshot.error.toString());
              return const Text("Something went wrong!");
            }
            if (snapshot.hasData) {
              return StatefulBuilder(builder: (context, setState2) {
                return ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 400),
                  child: Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Padding(
                          padding:
                              EdgeInsets.symmetric(horizontal: 20, vertical: 6),
                          child: Text(
                            "Select Schedule",
                            style: TextStyle(
                                fontSize: 18, fontWeight: FontWeight.bold),
                          ),
                        ),
                        const Divider(),
                        SingleChildScrollView(
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          child: ListView.builder(
                            shrinkWrap: true,
                            itemCount: snapshot.data?.length ?? 0,
                            itemBuilder: (BuildContext context, int index) {
                              final sch = snapshot.data![index];
                              return Card(
                                child: RadioListTile.adaptive(
                                  title: Text(
                                      '${sch.title} on ${sch.date.goodDate()}'),
                                  isThreeLine: true,
                                  subtitle: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      // Text(sch.date.goodDate()),
                                      Row(
                                        children: [
                                          Text(
                                            sch.startTime.goodTime(),
                                            style: const TextStyle(
                                                fontWeight: FontWeight.bold),
                                          ),
                                          const Text(" to "),
                                          Text(
                                            sch.endTime.goodTime(),
                                            style: const TextStyle(
                                                fontWeight: FontWeight.bold),
                                          ),
                                        ],
                                      ),
                                      Text(sch.desc),
                                    ],
                                  ),
                                  value: sch,
                                  groupValue: selectedDoc,
                                  onChanged: (value) {
                                    setState2(() {
                                      selectedDoc = value;
                                    });
                                  },
                                ),
                              );
                            },
                          ),
                        ),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            ElevatedButton.icon(
                              style: ElevatedButton.styleFrom(
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(6)),
                                  backgroundColor: Colors.red),
                              onPressed: () => Navigator.pop(context),
                              icon:
                                  const Icon(Icons.close, color: Colors.white),
                              label: const Text(
                                "Cancel",
                                style: TextStyle(color: Colors.white),
                              ),
                            ),
                            const SizedBox(width: 12),
                            ElevatedButton.icon(
                              style: ElevatedButton.styleFrom(
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(6)),
                                  backgroundColor: Colors.green),
                              onPressed: () async {
                                try {
                                  if (selectedDoc == null || loading) return;
                                  loading = true;
                                  final rId = getRandomId(6);
                                  final data = {
                                    "id": rId,
                                    "scheduledOn": FieldValue.serverTimestamp(),
                                    "schDocId": selectedDoc?.docId,
                                    "date": selectedDoc?.date,
                                    "courseId": widget.course.course?.docId,
                                    "uCourseId": widget.course.myCourse.docId,
                                    "uid": FBAuth.auth.currentUser?.uid,
                                    "passed": false,
                                    "canReSchedule": false,
                                  };
                                  final batch = FBFireStore.fs.batch();
                                  batch.set(FBFireStore.scheduled.doc(), data);
                                  batch.update(
                                      FBFireStore.userCourses
                                          .doc(widget.course.myCourse.docId),
                                      {"examSchedules.$rId": data});
                                  await batch.commit();
                                  loading = false;
                                  if (context.mounted) {
                                    showAppSnackBar(context, "Done",
                                        "Your offline exam was scheduled!");
                                    if (context.mounted) Navigator.pop(context);
                                  }
                                } catch (e) {
                                  debugPrint(e.toString());
                                }
                              },
                              icon:
                                  const Icon(Icons.check, color: Colors.white),
                              label: const Text(
                                "Confirm",
                                style: TextStyle(color: Colors.white),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              });
            }
            return Padding(
              padding: const EdgeInsets.all(20),
              child: loaderWave(color: appColorOne),
            );
          }),
    );
  }
}

class ExamInfoPop extends StatelessWidget {
  const ExamInfoPop({super.key, required this.sch, required this.course});
  final ExamSchedule sch;
  final DashboardCourse course;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: FutureBuilder<ScheduleModel>(
          future: FBFireStore.schedules.doc(sch.schDocId).get().then((value) =>
              ScheduleModel(
                  docId: value.id,
                  title: value['title'],
                  courseId: value['courseId'],
                  date: value['date'].toDate(),
                  startTime: value['startTime'].toDate(),
                  endTime: value['endTime'].toDate(),
                  desc: value['desc'])),
          builder: (context, snapshot) {
            if (snapshot.hasError) {
              debugPrint(snapshot.error.toString());
              return const Text("Something went wrong!");
            }
            if (snapshot.hasData) {
              return ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 400),
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Column(mainAxisSize: MainAxisSize.min, children: [
                    const Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: 20, vertical: 6),
                      child: Text(
                        "Exam Schedule",
                        style: TextStyle(
                            fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                    ),
                    const Divider(),
                    Row(
                      children: [
                        Text(
                          sch.date.toDate().goodDayDate(),
                          style: const TextStyle(fontSize: 18),
                        ),
                      ],
                    ),
                    Row(
                      children: [
                        Text(
                          snapshot.data!.startTime.goodTime(),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const Text(" to "),
                        Text(
                          snapshot.data!.endTime.goodTime(),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                    Text(snapshot.data!.desc),
                    const SizedBox(height: 12),
                    Center(
                      child: ElevatedButton.icon(
                        onPressed: () => onDownload(context, snapshot.data!),
                        icon: const Icon(Icons.file_download_outlined),
                        label: const Text("Hall Ticket"),
                      ),
                    ),
                  ]),
                ),
              );
            }
            return Padding(
              padding: const EdgeInsets.all(20),
              child: loaderWave(color: appColorOne),
            );
          }),
    );
  }

  onDownload(BuildContext context, ScheduleModel data) async {
    try {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
            await Future.delayed(const Duration(milliseconds: 500));
            DocGen.generateHallTicket(
                    data, Get.find<HomeController>().userData!, course)
                .then((value) => context.pop());
          });
          return const AlertDialog(
            title: Text("Please Wait"),
            content: Text("Processing..."),
          );
        },
      );
    } catch (e) {
      debugPrint(e.toString());
    }
  }
}
