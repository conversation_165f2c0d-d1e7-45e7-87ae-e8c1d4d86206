import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:wellfed/utils/consts.dart';
import '../../../models/certi_model.dart';
import '../../../models/dashboard_course.dart';
import '../../../services/docs/gen.dart';
import '../../../utils/firebase.dart';
import '../../../utils/other.dart';
import '../../../utils/router.dart';

class CertiCourseCard extends StatelessWidget {
  const CertiCourseCard({
    super.key,
    required this.course,
  });
  final DashboardCourse course;

  @override
  Widget build(BuildContext context) {
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: AspectRatio(
          aspectRatio: 1.5 / 1,
          child: Stack(
            fit: StackFit.expand,
            children: [
              FadeInImage.memoryNetwork(
                  placeholderErrorBuilder: (context, error, stackTrace) =>
                      Image.memory(placeholderGrad),
                  placeholder: placeholderGrad,
                  fit: BoxFit.cover,
                  image: course.course!.imageUrl),
              Container(
                decoration: const BoxDecoration(
                    gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                      Colors.black12,
                      Colors.black54,
                    ])),
              ),
              Column(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Container(
                    // margin: const EdgeInsets.all(20),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: const BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.vertical(
                            top: Radius.circular(4),
                            bottom: Radius.circular(11))),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                course.course?.courseId ?? "",
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: const TextStyle(
                                    fontWeight: FontWeight.bold),
                              ),
                              Text(
                                course.course?.title ?? "",
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: const TextStyle(),
                              ),
                              const SizedBox(height: 4),
                              const Divider(),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  _copyButton(),
                                  TextButton.icon(
                                    onPressed: () => _onDownload(context),
                                    icon: const Icon(
                                        CupertinoIcons.arrow_down_to_line_alt,
                                        size: 20),
                                    label: const Text("Certificate"),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 8),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  StatefulBuilder _copyButton() {
    bool copied = false;
    return StatefulBuilder(builder: (context, setState2) {
      return TextButton.icon(
        onPressed: () async {
          await _onCopy();
          setState2(() => copied = true);
          await Future.delayed(const Duration(milliseconds: 1500));
          setState2(() => copied = false);
        },
        icon: Icon(
          copied ? CupertinoIcons.check_mark_circled : CupertinoIcons.link,
          size: 20,
          color: copied ? Colors.green : null,
        ),
        label: Text(
          copied ? "Copied" : "Copy",
          style: TextStyle(color: copied ? Colors.green : null),
        ),
      );
    });
  }

  _onCopy() async {
    try {
      await Clipboard.setData(ClipboardData(
          text: '$currentHostUrl${Routes.certi}/${course.myCourse.certiUrl}'));
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  _onDownload(BuildContext context) async {
    try {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
            await Future.delayed(const Duration(milliseconds: 500));
            final certiDoc = await FBFireStore.certis
                .doc(course.myCourse.docId)
                .get()
                .then((value) => CertiModel.fromSnap(value));
            DocGen.generateCerti(certiDoc).then((value) => context.pop());
          });
          return const AlertDialog(
            title: Text("Please Wait"),
            content: Text("Processing certificate..."),
          );
        },
      );
    } catch (e) {
      debugPrint(e.toString());
    }
  }
}
