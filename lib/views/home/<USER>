import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:wellfed/utils/methods.dart';
import 'package:wellfed/utils/theme.dart';
import '../../controllers/home_ctrl.dart';
import '../../utils/router.dart';
import '../webview/webview.dart';

class WellFedDrawer extends StatelessWidget {
  const WellFedDrawer({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Column(
        children: [
          _logo(context),
          const Divider(),
          Expanded(child: SingleChildScrollView(child: _buttonCol(context))),
        ],
      ),
    );
  }

  Widget _logo(BuildContext context) => InkWell(
        hoverColor: Colors.transparent,
        onTap: () => context.go(Routes.home),
        child: Padding(
            padding: const EdgeInsets.fromLTRB(20, 30, 40, 20),
            child: Image.asset('assets/logo.png')),
      );
  TextStyle _appBarTextStyle() => const TextStyle(fontSize: 15);

  Column _buttonCol(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextButton(
            style: _appbarButtonStyle(),
            onPressed: () {
              homeScafKey.currentState?.closeDrawer();
              context.go(Routes.home);
            },
            child: Row(
              children: [
                const Icon(CupertinoIcons.home),
                const SizedBox(width: 14),
                Text(
                  "Home",
                  style: _appBarTextStyle(),
                ),
              ],
            )),
        TextButton(
            style: _appbarButtonStyle(),
            onPressed: () {
              homeScafKey.currentState?.closeDrawer();
              context.go(isLoggedIn() ? Routes.mycourses : Routes.login);
            },
            child: Row(
              children: [
                const Icon(CupertinoIcons.doc_chart),
                const SizedBox(width: 14),
                Text(
                  "My Courses",
                  style: _appBarTextStyle(),
                ),
              ],
            )),
        TextButton(
            style: _appbarButtonStyle(),
            onPressed: () {
              homeScafKey.currentState?.closeDrawer();
              context.go(Routes.courses);
            },
            child: Row(
              children: [
                const Icon(CupertinoIcons.list_bullet_below_rectangle),
                const SizedBox(width: 14),
                Text(
                  "Courses",
                  style: _appBarTextStyle(),
                ),
              ],
            )),
        TextButton(
            style: _appbarButtonStyle(),
            onPressed: () async {
              homeScafKey.currentState?.closeDrawer();
              if (GoRouterState.of(context).uri.toString() != Routes.home) {
                context.go(Routes.home, extra: "entr");
              }
              homeScroll("entr");
              // scrollTo(5000);
            },
            child: Row(
              children: [
                const Icon(CupertinoIcons.shield),
                const SizedBox(width: 14),
                Text(
                  "For Enterprise",
                  style: _appBarTextStyle(),
                ),
              ],
            )),
        TextButton(
            style: _appbarButtonStyle(),
            onPressed: () async {
              homeScafKey.currentState?.closeDrawer();
              if (GoRouterState.of(context).uri.toString() != Routes.home) {
                context.go(Routes.home, extra: 'prc');
              }
              homeScroll("prc");
              // scrollTo(6000);
            },
            child: Row(
              children: [
                const Icon(CupertinoIcons.arrow_2_squarepath),
                const SizedBox(width: 14),
                Text(
                  "Our Process",
                  style: _appBarTextStyle(),
                ),
              ],
            )),
      ],
    );
  }

/*   void scrollTo(int yPos) {
    try {
      final ctrl = Get.find<HomeController>().webviewController;
      if (ctrl != null) {
        ctrl.scrollTo(0, yPos);
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  } */

  ButtonStyle _appbarButtonStyle() {
    return ButtonStyle(
        foregroundColor: WidgetStateProperty.resolveWith<Color?>(
          (Set<WidgetState> states) {
            if (states.contains(WidgetState.hovered)) {
              return appColorOne;
            }
            if (states.contains(WidgetState.focused) ||
                states.contains(WidgetState.pressed)) {
              return appColorTwo;
            }
            return null;
          },
        ),
        padding: const WidgetStatePropertyAll(
            EdgeInsets.symmetric(horizontal: 24, vertical: 20)),
        shape: WidgetStatePropertyAll(
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(45))));
  }
}
