import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:wellfed/controllers/home_ctrl.dart';
import 'package:wellfed/views/home/<USER>';
import 'appbar.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key, required this.child});
  final StatefulNavigationShell child;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: homeScafKey,
      appBar: const WellFedAppbar(),
      drawer: const WellFedDrawer(),
      body: child,
    );
  }
}
