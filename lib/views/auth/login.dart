// ignore_for_file: use_build_context_synchronously

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:wellfed/controllers/home_ctrl.dart';
import 'package:wellfed/models/cart_model.dart';
import 'package:wellfed/models/checkout_model.dart';
import 'package:wellfed/utils/firebase.dart';
import 'package:wellfed/utils/loaders.dart';
import 'package:wellfed/utils/methods.dart';
import 'package:wellfed/utils/router.dart';

class LoginPage extends StatefulWidget {
  final String? fromPath;
  final String? courseId;
  final String? addCourseId;
  const LoginPage({super.key, this.fromPath, this.courseId, this.addCourseId});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final HomeController homeController = Get.find<HomeController>();

  final emailCtrl = TextEditingController();
  final passCtrl = TextEditingController();
  final loginErrorText = TextEditingController();
  bool loading = false;
  bool emailError = false;
  bool passError = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Center(
      child: Column(
        children: [
          const Spacer(flex: 3),
          Image.asset(
            'assets/logo.png',
            width: 240,
          ),
          Container(
            margin: const EdgeInsets.all(20),
            padding: const EdgeInsets.all(28),
            constraints: const BoxConstraints(maxWidth: 450),
            decoration: ShapeDecoration(
                color: Colors.blueGrey.shade50,
                shape: ContinuousRectangleBorder(
                    borderRadius: BorderRadius.circular(24))),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  "Sign In",
                  style: TextStyle(fontSize: 32, fontWeight: FontWeight.w200),
                ),
                const SizedBox(height: 16),
                Padding(
                  padding: const EdgeInsets.only(left: 4.0),
                  child: Text(
                    "EMAIL",
                    style: _textStyle(),
                  ),
                ),
                const SizedBox(height: 8),
                Material(
                  elevation: 2,
                  borderRadius: BorderRadius.circular(6),
                  child: TextField(
                    controller: emailCtrl,
                    keyboardType: TextInputType.emailAddress,
                    decoration: _decor().copyWith(
                        hintText: "Email Address",
                        prefixIcon: const Icon(CupertinoIcons.mail_solid)),
                  ),
                ),
                if (emailError)
                  const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 6.0, vertical: 4),
                    child: Text(
                      "Enter a valid email",
                      style: TextStyle(color: Colors.red),
                    ),
                  ),
                const SizedBox(height: 20),
                Text(
                  "PASSWORD",
                  style: _textStyle(),
                ),
                const SizedBox(height: 8),
                Material(
                  elevation: 2,
                  borderRadius: BorderRadius.circular(6),
                  child: TextField(
                    controller: passCtrl,
                    obscureText: true,
                    keyboardType: TextInputType.visiblePassword,
                    onSubmitted: (value) => _onSignIn(),
                    decoration: _decor().copyWith(
                        hintText: "Enter Password",
                        prefixIcon:
                            const Icon(CupertinoIcons.lock_shield_fill)),
                  ),
                ),
                if (passError)
                  const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 6.0, vertical: 4),
                    child: Text(
                      "Enter atleast 6 characters",
                      style: TextStyle(color: Colors.red),
                    ),
                  ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    InkWell(
                        onTap: _forgotPass,
                        child: Text(
                          "Forgot Password?",
                          style:
                              TextStyle(color: Theme.of(context).primaryColor),
                        ))
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                              backgroundColor: Theme.of(context).primaryColor,
                              shape: ContinuousRectangleBorder(
                                  borderRadius: BorderRadius.circular(24)),
                              minimumSize: const Size.fromHeight(52)),
                          onPressed: _onSignIn,
                          child: loading
                              ? loaderWave()
                              : const Text(
                                  "SIGN IN",
                                  style: TextStyle(color: Colors.white),
                                )),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Center(
                  child: Text(
                    "OR",
                    style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.grey.shade600),
                  ),
                ),
                const SizedBox(height: 16),
                Center(
                  child: SizedBox(
                    width: 180,
                    height: 44,
                    child: ElevatedButton(
                      onPressed: () => context.go(Routes.register),
                      style: ButtonStyle(
                        backgroundColor: WidgetStateProperty.all(Colors.white),
                        padding: WidgetStateProperty.all(
                            const EdgeInsets.symmetric(vertical: 12.0)),
                        shape: WidgetStateProperty.all(RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(6.0))),
                      ),
                      child: const Text("Create New Account",
                          style: TextStyle(color: Colors.black)),
                    ),
                  ),
                ),

                // Padding(
                //     padding: const EdgeInsets.symmetric(horizontal: 8.0),
                //     child: ElevatedButton(
                //         onPressed: () => context.go(Routes.register),
                //         child: const Text("Create New Account"))

                //     // Row(
                //     //   children: [
                //     //     const Text(
                //     //       "Don't have an account? ",
                //     //       style: TextStyle(),
                //     //     ),
                //     //     InkWell(
                //     //       borderRadius: BorderRadius.circular(12),
                //     //       onTap: () => context.go(Routes.register),
                //     //       child: Text(
                //     //         "Register",
                //     //         style:
                //     //             TextStyle(color: Theme.of(context).primaryColor),
                //     //       ),
                //     //     )
                //     //   ],
                //     // ),
                //     ),
                if (loginErrorText.text.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 6.0, vertical: 4),
                    child: Text(
                      loginErrorText.text,
                      style: const TextStyle(color: Colors.red),
                    ),
                  ),
              ],
            ),
          ),
          const Spacer(flex: 4),
          // const DevelopedByDefault(),
          const SizedBox(height: 4),
        ],
      ),
    ));
  }

  void _forgotPass() async {
    try {
      if (!emailCtrl.text.isEmail) {
        if (context.mounted) {
          showAppSnackBar(context, "Sorry", "Please, enter a valid email!");
        }
        return;
      }
      setState(() => loading = true);
      await FBAuth.auth.sendPasswordResetEmail(email: emailCtrl.text);
      setState(() => loading = false);
      if (context.mounted) {
        showAppSnackBar(context, "Done", "Password reset email sent!");
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  void _onSignIn() async {
    try {
      if (loading) return;
      if (_validate()) {
        setState(() => loading = true);

        await FirebaseAuth.instance.signInWithEmailAndPassword(
          email: emailCtrl.text,
          password: passCtrl.text,
        );

        setState(() => loading = false);
        loginErrorText.clear();

        // If there's a courseId to add to cart (from addCourseId param)
        if (widget.addCourseId != null) {
          final toAddCourse = CartCourseModel(courseId: widget.addCourseId!);
          await homeController.addToDBCart(toAddCourse);
          homeController.calculateCartTotal();
          showAppSnackBar(context, "Congratulations",
              "Course was successfully added to your cart!");
        }

        // Redirect logic - special handling for checkout with courseId
        if (widget.fromPath == Routes.checkout && widget.courseId != null) {
          if (context.mounted) {
            context.go(
              Routes.checkout,
              extra: CheckoutModel(
                checkoutCourses: [CartCourseModel(courseId: widget.courseId!)],
                enableEdit: true,
              ),
            );
            return;
          }
        }

        // Redirect to other fromPath or home by default
        if (widget.fromPath != null && widget.fromPath!.isNotEmpty) {
          if (context.mounted) {
            context.go(widget.fromPath!);
            return;
          }
        }

        if (context.mounted) {
          context.go(Routes.home);
        }
      }
    } catch (e) {
      loginErrorText.text = _getErrorMessage(e.toString());
      setState(() => loading = false);
      debugPrint(e.toString());
    }
  }

  // void _onSignIn() async {
  //   try {
  //     if (loading) return;
  //     if (_validate()) {
  //       setState(() => loading = true);
  //       await FirebaseAuth.instance.signInWithEmailAndPassword(
  //           email: emailCtrl.text, password: passCtrl.text);
  //       setState(() => loading = false);
  //       loginErrorText.clear();

  //       // Redirect logic using fromPath:
  //       final redirectPath = widget.fromPath;
  //       if (redirectPath != null && redirectPath.isNotEmpty) {
  //         if (context.mounted) {
  //           context.go(redirectPath);
  //           print("redirecting to $redirectPath");
  //         }
  //       } else {
  //         if (context.mounted) {
  //           context.go(Routes.home);
  //           print("redirecting to home");
  //         }
  //       }
  //     }
  //   } on FirebaseAuthException catch (e) {
  //     loginErrorText.text = _getErrorMessage(e.message ?? "");
  //     setState(() => loading = false);
  //     debugPrint(e.toString());
  //   }
  // }

  // void _onSignIn() async {
  //   try {
  //     if (loading) return;
  //     if (_validate()) {
  //       setState(() => loading = true);
  //       await FirebaseAuth.instance.signInWithEmailAndPassword(
  //           email: emailCtrl.text, password: passCtrl.text);
  //       setState(() => loading = false);
  //       loginErrorText.clear();
  //       if (context.mounted) context.go(Routes.home);
  //     }
  //   } on FirebaseAuthException catch (e) {
  //     loginErrorText.text = _getErrorMessage(e.message ?? "");
  //     setState(() => loading = false);
  //     debugPrint(e.toString());
  //   }
  // }

  bool _validate() {
    try {
      bool validate = true;
      if (!emailCtrl.text.isEmail) {
        validate = false;
        emailError = true;
      } else {
        emailError = false;
      }
      if (passCtrl.text.length < 6) {
        validate = false;
        passError = true;
      } else {
        passError = false;
      }
      setState(() {});
      return validate;
    } catch (e) {
      return false;
    }
  }

  String _getErrorMessage(String msg) {
    if (msg.contains("user-not-found")) {
      return "No user found with provided email.";
    } else if (msg.contains("wrong-password")) {
      return "Incorrect Password!";
    } else {
      return "Something went wrong!";
    }
  }

  InputDecoration _decor() {
    return InputDecoration(
      filled: true,
      fillColor: Colors.white,
      enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6), borderSide: BorderSide.none),
      errorBorder: InputBorder.none,
    );
  }

  TextStyle _textStyle() {
    return TextStyle(
        fontWeight: FontWeight.bold,
        fontSize: 12,
        color: Colors.blueGrey.shade700);
  }
}
