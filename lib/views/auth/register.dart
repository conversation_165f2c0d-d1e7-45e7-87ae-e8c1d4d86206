import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:wellfed/utils/consts.dart';
import 'package:wellfed/utils/firebase.dart';
import 'package:wellfed/utils/loaders.dart';
import 'package:wellfed/utils/methods.dart';
import 'package:wellfed/utils/router.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:wellfed/utils/theme.dart';

import '../../utils/other.dart';

class RegisterPage extends StatefulWidget {
  const RegisterPage({super.key, required this.reg});
  final bool reg;

  @override
  State<RegisterPage> createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage> {
  final formKey = GlobalKey<FormState>();
  final nameCtrl = TextEditingController();
  final phoneCtrl = TextEditingController();
  final branchCtrl = TextEditingController();
  final emailCtrl = TextEditingController();
  final passCtrl = TextEditingController();
  final rePassCtrl = TextEditingController();
  PhoneNumber? phoneNumber = PhoneNumber(isoCode: "IN", dialCode: "+91");
  bool isEnterprise = false;
  bool loading = false;
  @override
  void initState() {
    super.initState();
    isEnterprise = widget.reg;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: MediaQuery.sizeOf(context).width < mobileMinSize
            ? SingleChildScrollView(
                padding: const EdgeInsets.symmetric(vertical: 20),
                child: Column(
                  // crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 12),
                    const Row(),
                    _logo(),
                    Container(
                      margin: const EdgeInsets.all(20),
                      padding: const EdgeInsets.all(20),
                      decoration: _boxDecor(),
                      child: ConstrainedBox(
                        constraints: const BoxConstraints(maxWidth: 450),
                        child: _form(context),
                      ),
                    ),
                    // const DevelopedByDefault(),
                    const SizedBox(height: 4),
                  ],
                ),
              )
            : Row(
                children: [
                  Expanded(
                      child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const SizedBox(height: 4),
                      Center(
                        child: _logo(350),
                      ),
                      // const Padding(
                      //   padding: EdgeInsets.only(bottom: 4.0),
                      //   child: DevelopedByDefault(),
                      // ),
                    ],
                  )),
                  Expanded(
                    child: Column(
                      children: [
                        Expanded(
                          child: Container(
                            color: Colors.blueGrey.shade50,
                            child: SingleChildScrollView(
                              padding: const EdgeInsets.symmetric(
                                  vertical: 20, horizontal: 20),
                              child: Column(
                                // crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const SizedBox(height: 40),
                                  const Row(),
                                  ConstrainedBox(
                                    constraints:
                                        const BoxConstraints(maxWidth: 500),
                                    child: _form(context),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ));
  }

  ShapeDecoration _boxDecor() {
    return ShapeDecoration(
        color: Colors.blueGrey.shade50,
        shape:
            ContinuousRectangleBorder(borderRadius: BorderRadius.circular(24)));
  }

  Padding _logo([double? size = 240]) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Image.asset(
        'assets/logo.png',
        width: size,
      ),
    );
  }

  Form _form(BuildContext context) {
    return Form(
      key: formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // const SizedBox(height: 20),
          const Text(
            "Welcome,",
            style: TextStyle(fontWeight: FontWeight.w500, fontSize: 28),
          ),
          const Text(
            "There are many variations of passages of Lorem Ipsum available",
            style: TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 12),
          _accountTypeRow(),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text(isEnterprise ? enterpriseUserText : individualUserText),
          ),
          // const SizedBox(height: 20),
          TextFormField(
            controller: nameCtrl,
            keyboardType: TextInputType.name,
            validator: (value) => value!.isEmpty ? "Required" : null,
            decoration: _decor().copyWith(
                hintText: isEnterprise ? "Enterprise Name" : "Full Name",
                prefixIcon: const Icon(CupertinoIcons.mail_solid)),
          ),
          const SizedBox(height: 20),
          InternationalPhoneNumberInput(
            initialValue: phoneNumber,
            selectorConfig: const SelectorConfig(
                leadingPadding: 12,
                trailingSpace: false,
                setSelectorButtonAsPrefixIcon: true,
                selectorType: PhoneInputSelectorType.DIALOG),
            keyboardType: const TextInputType.numberWithOptions(signed: true),
            inputDecoration: _decor().copyWith(hintText: "Contact Number"),
            validator: (p0) =>
                p0!.isPhoneNumber ? null : "Enter a valid phone number",
            onInputChanged: (value) => phoneNumber = value,
            textFieldController: phoneCtrl,
          ),
          if (isEnterprise) const SizedBox(height: 20),
          if (isEnterprise)
            TextFormField(
              controller: branchCtrl,
              keyboardType: TextInputType.text,
              validator: (value) => value!.isEmpty ? "Required" : null,
              decoration: _decor().copyWith(
                  hintText: "Branch/ Location",
                  prefixIcon: const Icon(CupertinoIcons.map_pin_ellipse)),
            ),
          const SizedBox(height: 20),
          TextFormField(
            controller: emailCtrl,
            keyboardType: TextInputType.emailAddress,
            validator: (value) =>
                !value!.isEmail ? "Enter a valid email" : null,
            decoration: _decor().copyWith(
                hintText: "Email Address",
                prefixIcon: const Icon(CupertinoIcons.mail_solid)),
          ),
          const SizedBox(height: 20),
          TextFormField(
            controller: passCtrl,
            obscureText: true,
            keyboardType: TextInputType.visiblePassword,
            onFieldSubmitted: (value) => _onSubmit(),
            validator: (value) =>
                value!.length < 6 ? "Enter atleast 6 characters" : null,
            decoration: _decor().copyWith(
                hintText: "Enter Password",
                prefixIcon: const Icon(CupertinoIcons.lock_shield_fill)),
          ),
          const SizedBox(height: 20),
          TextFormField(
            controller: rePassCtrl,
            obscureText: true,
            keyboardType: TextInputType.visiblePassword,
            onFieldSubmitted: (value) => _onSubmit(),
            validator: (value) =>
                value != passCtrl.text ? "Must be same as password" : null,
            decoration: _decor().copyWith(
                hintText: "Re Enter Password",
                prefixIcon: const Icon(CupertinoIcons.lock_shield_fill)),
          ),
          const SizedBox(height: 28),
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).primaryColor,
                        shape: ContinuousRectangleBorder(
                            borderRadius: BorderRadius.circular(24)),
                        minimumSize: const Size.fromHeight(52)),
                    onPressed: _onSubmit,
                    child: loading
                        ? loaderWave()
                        : const Text(
                            "Submit",
                            style: TextStyle(color: Colors.white),
                          )),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Row(
              children: [
                const Text(
                  "Alredy have an account? ",
                  style: TextStyle(),
                ),
                InkWell(
                  borderRadius: BorderRadius.circular(12),
                  onTap: () => context.go(Routes.login),
                  child: Text(
                    "Sign In",
                    style: TextStyle(color: Theme.of(context).primaryColor),
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  Row _accountTypeRow() {
    return Row(
      children: [
        // Padding(
        //   padding: const EdgeInsets.only(left: 4.0),
        //   child: Text(
        //     "ACCOUNT TYPE ",
        //     style: _textStyle(),
        //   ),
        // ),
        // const SizedBox(width: 8),
        InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () => setState(() => isEnterprise = false),
          child: AnimatedContainer(
            padding: const EdgeInsets.fromLTRB(4, 4, 12, 4),
            duration: const Duration(milliseconds: 300),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: !isEnterprise
                    ? appColorOne.withOpacity(.1)
                    : Colors.grey.shade100),
            child: Row(
              children: [
                Radio.adaptive(
                  activeColor: appColorOne,
                  value: false,
                  groupValue: isEnterprise,
                  onChanged: (value) => setState(() => isEnterprise = false),
                ),
                Text(
                  "Individual",
                  style: TextStyle(
                      color:
                          !isEnterprise ? appColorOne : Colors.grey.shade700),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(width: 12),
        InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () => setState(() => isEnterprise = true),
          child: AnimatedContainer(
            padding: const EdgeInsets.fromLTRB(4, 4, 12, 4),
            duration: const Duration(milliseconds: 300),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: isEnterprise
                    ? appColorOne.withOpacity(.2)
                    : Colors.grey.shade100),
            child: Row(
              children: [
                Radio.adaptive(
                  activeColor: appColorOne,
                  value: true,
                  groupValue: isEnterprise,
                  onChanged: (value) => setState(() => isEnterprise = true),
                ),
                Text(
                  "Enterprise",
                  style: TextStyle(
                      color: isEnterprise ? appColorOne : Colors.grey.shade700),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _onSubmit() async {
    try {
      // showAppSnackBar(context, "This is msg!");
      // return;
      if (loading) return;
      if (formKey.currentState!.validate()) {
        final typeEnterprise = isEnterprise;
        if (!typeEnterprise && await _confirmData() != true) return;
        setState(() => loading = true);
        final result = await FirebaseAuth.instance
            .createUserWithEmailAndPassword(
                email: emailCtrl.text, password: passCtrl.text);
        await FBFireStore.users.doc(result.user!.uid).set({
          // Any change here need to be updated on server also.
          "createdBy": null,
          "name": nameCtrl.text,
          "contact": '${phoneNumber?.dialCode}${phoneCtrl.text}',
          "email": result.user!.email,
          "eId": typeEnterprise ? getRandomId(12) : null,
          "branch": branchCtrl.text,
          "linkedWith": {},
          "cartItems": {},
          "qtyPurchased": 0,
          "valuePurchased": 0,
        });
        setState(() => loading = false);
        if (context.mounted) context.go(Routes.home);
      }
    } catch (e) {
      setState(() => loading = false);
      debugPrint(e.toString());
      showAppSnackBar(context, "Oh Snap!", _getErrorMessage(e.toString()));
    }
  }

  Future<bool?> _confirmData() async {
    return await showDialog<bool>(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('Confirm'),
        content: const Text(
            'Please, confirm your details. Provided fullname will be used for certification!'),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Confirm'),
          ),
        ],
      ),
    );
  }

  String _getErrorMessage(String msg) {
    if (msg.contains("email-already-in-use")) {
      return "Provided email is already used, try to login!";
    } else if (msg.contains("invalid-email")) {
      return "Provided email is invalid!";
    } else if (msg.contains("weak-password")) {
      return "Password is too weaks!";
    } else {
      return "Something went wrong!";
    }
  }

  InputDecoration _decor() {
    return InputDecoration(
      filled: true,
      fillColor: Colors.white,
      // fillColor: Colors.grey.shade100,
      enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6), borderSide: BorderSide.none),
      errorBorder: InputBorder.none,
    );
  }
}
