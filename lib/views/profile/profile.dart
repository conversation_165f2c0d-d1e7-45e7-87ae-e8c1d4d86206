import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';
import 'package:wellfed/controllers/home_ctrl.dart';
import 'package:wellfed/models/course_model.dart';
import 'package:wellfed/models/user_course_model.dart';
import 'package:wellfed/utils/firebase.dart';
import 'package:wellfed/utils/router.dart';
import '../../models/dashboard_course.dart';
import '../../utils/consts.dart';
import '../../utils/methods.dart';
import '../mycourses/dashboard/courses_box.dart';
import '../mycourses/dashboard/overview_box.dart';
import '../webview/copyright.dart';
import '../webview/footer.dart';
import '../webview/webview.dart';

class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    bool large = size.width > breakPointLarge;
    bool small = size.width < breakPointMid;
    return SingleChildScrollView(
      child: Column(
        children: [
          Padding(
              padding: !large
                  ? EdgeInsets.symmetric(
                      horizontal: small ? 28 : size.width * .1,
                      vertical: small ? 34 : size.width * .06)
                  : homeLargeInsects(size),
              child: GetBuilder<HomeController>(
                init: Get.find<HomeController>(),
                builder: (_) {
                  List<DashboardCourse> myCourses = <DashboardCourse>[];
                  for (var element in _.myCourses) {
                    myCourses.add(DashboardCourse(
                        _.courseList.firstWhereOrNull(
                            (e) => e.docId == element.courseId),
                        element));
                  }
                  final crseCount = getCourseQtyCount(myCourses);
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      _userDetailBox(context, _, !large, _.isEnterprise,
                          crseCount, myCourses),
                      const SizedBox(height: 28),
                      _myCoursesText(),
                      // const Divider(),
                      const SizedBox(height: 12),
                      ListView.builder(
                        physics: const ClampingScrollPhysics(),
                        itemCount: myCourses.length,
                        shrinkWrap: true,
                        itemBuilder: (BuildContext context, int index) {
                          final crse = myCourses[index];
                          final expiresIn = crse.myCourse.endDate!
                              .toDate()
                              .difference(DateTime.now())
                              .inDays;
                          final percentage = getPercentage(
                              DashboardCourse(crse.course, crse.myCourse));
                          return ProfileCourseCard(
                              isEnterprise: _.isEnterprise,
                              crse: crse.course,
                              uCrse: crse.myCourse,
                              expiresIn: expiresIn,
                              percentage: percentage);
                        },
                      ),
                      const SizedBox(height: 20),
                    ],
                  );
                },
              )),
          FooterBox(size: size),
          CopyRightBar(size: size),
        ],
      ),
    );
  }

  Widget _totalBox(HomeController _, CourseQtyCount cQC) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          _.isEnterprise ? "Total Courses" : "In Progress",
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(width: 20),
        Text(
          _.isEnterprise
              ? cQC.total.toString().padLeft(2, "0")
              : _.myCourses
                  .where((element) => !element.completed)
                  .length
                  .toString()
                  .padLeft(2, "0"),
          style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              fontFamily: 'Montserrat'),
        ),
      ],
    );
  }

  Widget _completedBox(HomeController _, CourseQtyCount cQC) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          _.isEnterprise ? "Courses Assigned" : "Courses Completed",
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(width: 20),
        Text(
          _.isEnterprise
              ? cQC.assigned.toString().padLeft(2, "0")
              : _.myCourses
                  .where((element) => element.completed)
                  .length
                  .toString()
                  .padLeft(2, "0"),
          style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              fontFamily: 'Montserrat'),
        ),
      ],
    );
  }

  Widget _certiBox(HomeController _, CourseQtyCount cQC) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          _.isEnterprise ? "Unassigned Courses" : "Certificates",
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(width: 20),
        Text(
          _.isEnterprise
              ? (cQC.total - cQC.assigned).toString().padLeft(2, "0")
              : _.myCourses
                  .where((element) => element.certiUrl != null)
                  .length
                  .toString()
                  .padLeft(2, "0"),
          style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              fontFamily: 'Montserrat'),
        ),
      ],
    );
  }

  Row _myCoursesText() {
    return const Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(width: 12),
        Icon(
          CupertinoIcons.list_bullet_below_rectangle,
          size: 28,
        ),
        SizedBox(width: 8),
        Text(
          "My Courses",
          style: TextStyle(fontSize: 24),
        ),
      ],
    );
  }

  Widget _userDetailBox(BuildContext context, HomeController _, bool small,
      bool isEnterprise, CourseQtyCount cQC, List<DashboardCourse> crses) {
    return small
        ? Column(
            children: [
              Container(
                padding: const EdgeInsets.fromLTRB(40, 24, 40, 40),
                decoration: _detailContainerDecor(context),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          CupertinoIcons.person_circle,
                          size: 28,
                        ),
                        const SizedBox(width: 8),
                        const Text(
                          "My Profile",
                          style: TextStyle(fontSize: 24),
                        ),
                        const Spacer(),
                        ..._logoutButton(context),
                      ],
                    ),
                    const SizedBox(height: 6),
                    const Divider(),
                    const SizedBox(height: 12),
                    Wrap(
                      spacing: 20,
                      runSpacing: 6,
                      children: [
                        _nameAndNumber(_),
                        _emailAndBranch(_),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),
              ListTile(
                onTap: () => context.go(Routes.mycourses),
                title: Text(
                    isEnterprise ? "Total Courses" : "Courses in Progress"),
                trailing: Text(isEnterprise
                    ? cQC.total.toString().padLeft(2, "0")
                    : crses
                        .where((element) => !element.myCourse.completed)
                        .length
                        .toString()
                        .padLeft(2, "0")),
              ),
              ListTile(
                onTap: () => context.go(Routes.mycourses),
                title: Text(
                  isEnterprise ? "Courses Assigned" : "Courses Completed",
                ),
                trailing: Text(isEnterprise
                    ? cQC.assigned.toString().padLeft(2, "0")
                    : crses
                        .where((element) => element.myCourse.completed)
                        .length
                        .toString()
                        .padLeft(2, "0")),
              ),
              ListTile(
                onTap: () => context.go(Routes.mycourses),
                title: Text(isEnterprise
                    ? "Unassigned Courses"
                    : "Certificates Earned"),
                trailing: Text(isEnterprise
                    ? (cQC.total - cQC.assigned).toString().padLeft(2, "0")
                    : crses
                        .where((element) => element.myCourse.certiUrl != null)
                        .length
                        .toString()
                        .padLeft(2, "0")),
              ),
            ],
          )
        : Stack(
            alignment: Alignment.bottomCenter,
            children: [
              Container(
                margin: const EdgeInsets.only(bottom: 40),
                padding: const EdgeInsets.fromLTRB(40, 24, 40, 40),
                decoration: _detailContainerDecor(context),
                child: Row(
                  children: [
                    _personIcon(),
                    const SizedBox(width: 12),
                    _nameAndNumber(_),
                    const SizedBox(width: 12),
                    _emailAndBranch(_),
                    const SizedBox(width: 12),
                    const Spacer(),
                    Column(
                      children: _logoutButton(context),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 40),
                child: Material(
                  color: Colors.white,
                  elevation: 4,
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8)),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 20, vertical: 20),
                    child: Row(
                      children: [
                        Expanded(
                          child: Wrap(
                            spacing: 20,
                            runSpacing: 20,
                            alignment: WrapAlignment.spaceEvenly,
                            crossAxisAlignment: WrapCrossAlignment.center,
                            children: [
                              _totalBox(_, cQC),
                              _completedBox(_, cQC),
                              _certiBox(_, cQC),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              )
            ],
          );
  }

  ShapeDecoration _detailContainerDecor(BuildContext context) {
    return ShapeDecoration(
      color: Theme.of(context).primaryColor.withOpacity(.1),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      // shape: ContinuousRectangleBorder(
      //   borderRadius: BorderRadius.circular(45),
      // ),
    );
  }

  List<Widget> _logoutButton(BuildContext context) {
    return [
      if (isLoggedIn())
        OutlinedButton.icon(
          style: OutlinedButton.styleFrom(
            side: BorderSide(
                color: Theme.of(context).primaryColor.withOpacity(.4)),
            shape: ContinuousRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          onPressed: () => _logoutPopup(context),
          icon: const Icon(Icons.logout_rounded),
          label: const Text("Logout"),
        ),
    ];
  }

  Column _emailAndBranch(HomeController _) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.mail_outline_rounded,
              size: 20,
              color: Colors.grey.shade600,
            ),
            const SizedBox(width: 8),
            Text(
              _.userData?.email ?? "",
              style: const TextStyle(fontFamily: "Montserrat"),
            ),
          ],
        ),
        const SizedBox(height: 12),
        if (_.userData?.branch.isNotEmpty ?? false)
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.location_on_outlined,
                size: 20,
                color: Colors.grey.shade600,
              ),
              const SizedBox(width: 8),
              Text(
                _.userData?.branch ?? "",
                style: const TextStyle(fontFamily: "Montserrat"),
              )
            ],
          ),
      ],
    );
  }

  Column _nameAndNumber(HomeController _) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.badge_outlined,
              size: 20,
              color: Colors.grey.shade600,
            ),
            const SizedBox(width: 8),
            Text(
              _.userData?.name ?? "",
              style: const TextStyle(fontFamily: "Montserrat"),
            )
          ],
        ),
        const SizedBox(height: 12),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.call,
              size: 20,
              color: Colors.grey.shade600,
            ),
            const SizedBox(width: 8),
            Text(
              _.userData?.contact ?? "",
              style: const TextStyle(fontFamily: "Montserrat"),
            )
          ],
        ),
      ],
    );
  }

  Icon _personIcon() {
    return Icon(
      CupertinoIcons.person_circle,
      size: 100,
      color: Colors.grey.shade600,
    );
  }

  _logoutPopup(BuildContext context) async {
    try {
      final res = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text("Confirm"),
          content: const Text("Are you sure want to logout?"),
          actions: [
            TextButton(
              onPressed: () => context.pop(false),
              child: const Text("No"),
            ),
            TextButton(
              onPressed: () => context.pop(true),
              child: const Text("Yes"),
            ),
          ],
        ),
      );
      if (res == true) {
        await FBAuth.auth.signOut();
        if (context.mounted) context.go(Routes.home);
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  CourseQtyCount getCourseQtyCount(List<DashboardCourse> corses) {
    int total = 0;
    int assigned = 0;
    for (var element in corses) {
      total += element.myCourse.qty;
      assigned += element.myCourse.assigned;
    }
    return CourseQtyCount(total, assigned);
  }
}

class ProfileCourseCard extends StatelessWidget {
  const ProfileCourseCard({
    super.key,
    required this.crse,
    required this.uCrse,
    required this.expiresIn,
    required this.percentage,
    required this.isEnterprise,
  });

  final CourseModel? crse;
  final UserCourseModel uCrse;
  final int expiresIn;
  final double percentage;
  final bool isEnterprise;

  @override
  Widget build(BuildContext context) {
    return Card(
      surfaceTintColor: Colors.white,
      clipBehavior: Clip.hardEdge,
      child: ListTile(
        onTap: () => context.go('${Routes.course}/${crse?.docId}'),
        title: Text(crse?.title ?? ""),
        subtitle: isEnterprise
            ? Text.rich(
                TextSpan(
                    text: "Total: ",
                    style: TextStyle(color: Colors.grey.shade700),
                    children: [
                      TextSpan(
                        text: uCrse.qty.toString().padLeft(2, "0"),
                        style: const TextStyle(color: Colors.black),
                      ),
                      TextSpan(
                        text: " | Assigned: ",
                        style: TextStyle(color: Colors.grey.shade700),
                      ),
                      TextSpan(
                        text: uCrse.qty.toString().padLeft(2, "0"),
                        style: const TextStyle(color: Colors.black),
                      ),
                    ]),
              )
            : Text.rich(
                TextSpan(text: "Status:", children: [
                  TextSpan(
                    text: uCrse.completed
                        ? " Completed "
                        : expiresIn >= 0
                            ? "Expires in $expiresIn days"
                            : " Expired ",
                    style: TextStyle(
                        color: uCrse.completed ? Colors.green : Colors.red),
                  )
                ]),
              ),
        trailing: isEnterprise
            ? const CupertinoListTileChevron()
            : uCrse.completed
                ? IconButton(
                    tooltip: uCrse.certiUrl == null
                        ? "Waiting for Upload"
                        : "View Certi",
                    onPressed: () => uCrse.certiUrl != null
                        ? context.go('${Routes.certi}/${uCrse.certiUrl}')
                        : null,
                    icon: uCrse.certiUrl == null
                        ? const Icon(CupertinoIcons.hourglass)
                        : const Icon(CupertinoIcons.doc_append))
                : CircularPercentIndicator(
                    radius: 20.0,
                    lineWidth: 5.0,
                    percent: percentage,
                    // percent: 0.60,
                    // restartAnimation: true,
                    animationDuration: 1000,
                    animation: true,
                    center: Text(
                      "${percentage * 100}%",
                      style: const TextStyle(fontSize: 12),
                    ),
                    progressColor: Theme.of(context).primaryColor,
                  ),
      ),
    );
  }
}
