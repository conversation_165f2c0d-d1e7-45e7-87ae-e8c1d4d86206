import 'package:dynamic_path_url_strategy/dynamic_path_url_strategy.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wellfed/controllers/home_ctrl.dart';
import 'package:wellfed/utils/router.dart';
import 'package:wellfed/utils/theme.dart';
import 'firebase_options.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  // Use Flutter's built-in URL strategy for web
  setPathUrlStrategy();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
    Get.lazyPut(() => HomeController());
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: 'Wellfed',
      routerConfig: appRouter,
      debugShowCheckedModeBanner: false,
      theme: themeData,
    );
  }
}
