import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:wellfed/models/certi_model.dart';
import 'package:wellfed/models/schedule_model.dart';
import 'package:wellfed/services/docs/pdf_gens.dart';
import 'package:wellfed/services/docs/wid_gens.dart';

import '../../models/dashboard_course.dart';
import '../../models/user_model.dart';

class DocGen {
  static Future<Uint8List?> generateCerti(CertiModel certiModel,
      {bool download = true}) async {
    return PDFGen.generateCertiPdf(certiModel, download: download);
  }

  static Future<Uint8List?> generateHallTicket(
      ScheduleModel schModel, UserModel user, DashboardCourse crse,
      {bool download = true}) async {
    return PDFGen.generateHallTicketPdf(schModel, user, crse,
        download: download);
  }

  static Widget generateCertiWid(CertiModel certiModel) =>
      WidGen.generateCertiWid(certiModel);
}

const certiGoldenColors = [
  Color(0xffB57E10),
  Color(0xffF9DF7B),
  Color(0xffB57E10),
  Color(0xffF9DF7B),
];
