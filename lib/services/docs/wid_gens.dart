import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wellfed/services/docs/gen.dart';
import 'package:wellfed/utils/methods.dart';
import 'package:wellfed/utils/theme.dart';
import '../../models/certi_model.dart';

class WidGen {
  static Widget generateCertiWid(CertiModel certiModel) {
    return Stack(
      fit: StackFit.expand,
      children: [
        Image.asset('assets/certi.png'), //
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 40.0),
          child: Column(
            children: [
              const SizedBox(height: 20),
              const Spacer(flex: 3),
              Text(
                "THIS CERTIFICATE IS PROUDLY PRESENTED TO",
                style: TextStyle(
                    fontFamily: 'Roboto', color: Colors.grey.shade600),
              ),
              const SizedBox(height: 12),
              Text(
                certiModel.fullName,
                style: GoogleFonts.parisienne(
                    fontSize: certiModel.fullName.length > 20 ? 60 : 80,
                    height: 1.15),
              ),
              Container(
                height: 2,
                width: certiModel.fullName.length *
                    (certiModel.fullName.length > 20 ? 24 : 32),
                decoration: const BoxDecoration(
                  gradient: LinearGradient(colors: certiGoldenColors),
                ),
              ),
              const Spacer(),
              Text(
                "Each constellation is a region of the sky",
                style: TextStyle(
                  // style: appTitleTextStyle.copyWith(
                  // fontFamily: 'Montserrat',
                  fontSize: 24,
                  color: Colors.grey.shade900,
                  // fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                "Lorem Ipsum is simply dummy text of the printing and typesetting industry.\nLorem Ipsum has been the industry's standard dummy text ever since the 1500s,\nwhen an unknown printer took a galley of type and scrambled it to make a type specimen book.",
                textAlign: TextAlign.center,
                style: appTextStyleTwo.copyWith(),
              ),
              const SizedBox(height: 20),
              const Spacer(),
              Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Column(
                    children: [
                      Text(
                        certiModel.completedOn.toDate().goodDayDate(),
                        style: appTextStyleOne.copyWith(
                          fontSize: 18,
                        ),
                      ),
                      Container(
                        height: 2,
                        width: 140,
                        decoration: const BoxDecoration(
                          gradient: LinearGradient(colors: certiGoldenColors),
                        ),
                      ),
                      const SizedBox(height: 2),
                      const Text(
                        "DATE",
                        style: TextStyle(),
                      ),
                    ],
                  ),
                  Column(
                    children: [
                      Text(
                        "Signature",
                        style:
                            GoogleFonts.parisienne(fontSize: 28, height: 1.15),
                      ),
                      Container(
                        height: 2,
                        width: 140,
                        decoration: const BoxDecoration(
                          gradient: LinearGradient(colors: certiGoldenColors),
                        ),
                      ),
                      const SizedBox(height: 2),
                      const Text(
                        "DIRECTOR",
                        style: TextStyle(),
                      ),
                    ],
                  ),
                ],
              ),
              const Spacer(),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ],
    );
  }
}
