import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart';
import 'package:printing/printing.dart';
import 'package:wellfed/models/dashboard_course.dart';
import 'package:wellfed/models/schedule_model.dart';
import 'package:wellfed/utils/methods.dart';
import '../../models/certi_model.dart';
import '../../models/user_model.dart';
import '../../utils/consts.dart';

class PDFGen {
  static final certiGoldenPdfColors = [
    PdfColor.fromHex("B57E10"),
    PdfColor.fromHex("F9DF7B"),
    PdfColor.fromHex("B57E10"),
    PdfColor.fromHex("F9DF7B"),
  ];

  static Future<Uint8List?> generateCertiPdf(CertiModel certiModel,
      {bool download = true}) async {
    try {
      const fontPlus = 12.0;
      final pdf = Document();
      // final ByteData bgGraphic =
      //     await rootBundle.load(kDebugMode ? 'assets/certi.png' : 'certi.png');
      final ByteData bgGraphic = await rootBundle.load('assets/certi.png');
      Uint8List bgGraphicData = (bgGraphic).buffer.asUint8List();
      // final Uint8List nameFontFile =
      //     File('assets/google_fonts/Parisienne-Regular.ttf').readAsBytesSync();
      // final nameFont = Font.ttf(nameFontFile.buffer.asByteData());
      final nameFontFile =
          await rootBundle.load("assets/google_fonts/Parisienne-Regular.ttf");
      final nameFont = Font.ttf(nameFontFile);
      final robotoFontFile =
          await rootBundle.load("assets/google_fonts/Roboto-Bold.ttf");
      final robotoFont = Font.ttf(robotoFontFile);
      // final montserratFontFile =
      //     await rootBundle.load("google_fonts/Montserrat-Bold.ttf");
      // final montserratFont = Font.ttf(montserratFontFile);
      // final Uint8List robotoFontFile =
      //     File('assets/google_fonts/Roboto-Black.ttf').readAsBytesSync();
      // final robotoFont = Font.ttf(robotoFontFile.buffer.asByteData());
      pdf.addPage(Page(
        pageFormat: const PdfPageFormat(certiWidth * .8, certiHeight * .8),
        orientation: PageOrientation.landscape,
        build: (context) {
          return Stack(
            fit: StackFit.expand,
            children: [
              Image(
                MemoryImage(bgGraphicData),
                // height: certiHeight * .8,
                // width: certiWidth * .8,
              ), //
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 40.0),
                child: Column(
                  children: [
                    SizedBox(height: 20),
                    Spacer(flex: 3),
                    Text(
                      "THIS CERTIFICATE IS PROUDLY PRESENTED TO",
                      style: TextStyle(
                          font: robotoFont,
                          color: PdfColors.grey600,
                          fontSize: 14 + fontPlus),
                    ),
                    SizedBox(height: 12),
                    Stack(
                      alignment: Alignment.bottomCenter,
                      children: [
                        Text(
                          certiModel.fullName,
                          style: TextStyle(
                              fontSize:
                                  certiModel.fullName.length > 20 ? 100 : 160,
                              font: nameFont,
                              fontStyle: FontStyle.normal),
                        ),
                        Container(
                          height: 2,
                          margin: const EdgeInsets.only(bottom: 12),
                          width: certiModel.fullName.length *
                              (certiModel.fullName.length > 20 ? 40 : 54),
                          decoration: BoxDecoration(
                            gradient:
                                LinearGradient(colors: certiGoldenPdfColors),
                          ),
                        ),
                      ],
                    ),
                    Spacer(),
                    Text(
                      "Each ellation is a region of the sky",
                      style: TextStyle(
                        // style: appTitleTextStyle.copyWith(
                        fontSize: 24 + fontPlus,
                        // font: montserratFont,
                        color: PdfColors.grey900,
                        // fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      "Lorem Ipsum is simply dummy text of the printing and typesetting industry.\nLorem Ipsum has been the industry's standard dummy text ever since the 1500s,\nwhen an unknown printer took a galley of type and scrambled it to make a type specimen book.",
                      textAlign: TextAlign.center,
                      style: const TextStyle(fontSize: 14 + fontPlus),
                      // style: appTextStyleTwo.copyWith(),
                    ),
                    SizedBox(height: 20),
                    Spacer(),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Column(
                          children: [
                            Text(
                              certiModel.completedOn.toDate().goodDayDate(),
                              style: const TextStyle(
                                // style: appTextStyleOne.copyWith(
                                fontSize: 18 + fontPlus,
                              ),
                            ),
                            Container(
                              height: 2,
                              width: 140,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                    colors: certiGoldenPdfColors),
                              ),
                            ),
                            SizedBox(height: 2),
                            Text(
                              "DATE",
                              style: const TextStyle(fontSize: 14 + fontPlus),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            Text(
                              "Signature",
                              style: TextStyle(
                                  font: nameFont,
                                  fontSize: 28 + fontPlus,
                                  height: 1.15),
                            ),
                            Container(
                              height: 2,
                              width: 140,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                    colors: certiGoldenPdfColors),
                              ),
                            ),
                            SizedBox(height: 2),
                            Text(
                              "DIRECTOR",
                              style: const TextStyle(fontSize: 14 + fontPlus),
                            ),
                          ],
                        ),
                      ],
                    ),
                    Spacer(),
                    SizedBox(height: 20),
                  ],
                ),
              ),
            ],
          );
        },
      ));
      // await Printing.layoutPdf(
      //     onLayout: (format) async => await pdf.save(),
      //     name: DateTime.now().microsecondsSinceEpoch.toString());
      await Printing.sharePdf(
          bytes: await pdf.save(),
          filename:
              '${certiModel.fullName /* ?? DateTime.now().microsecondsSinceEpoch */}.pdf');
      return null;
    } catch (e) {
      debugPrint(e.toString());
      return null;
    }
  }

  static Future<Uint8List?> generateHallTicketPdf(
      ScheduleModel schModel, UserModel user, DashboardCourse crse,
      {bool download = true}) async {
    try {
      final pdf = Document();
      final ByteData bgGraphic = await rootBundle.load('assets/ht.png');
      // final ByteData bgGraphic = await rootBundle.load('assets/ht.png');
      Uint8List bgGraphicData = (bgGraphic).buffer.asUint8List();
      // final Uint8List nameFontFile =
      //     File('assets/google_fonts/Parisienne-Regular.ttf').readAsBytesSync();
      // final nameFont = Font.ttf(nameFontFile.buffer.asByteData());
      final robotoFontFile =
          await rootBundle.load("assets/google_fonts/Roboto-Bold.ttf");
      final robotoFont = Font.ttf(robotoFontFile);
      // final montserratFontFile =
      //     await rootBundle.load("google_fonts/Montserrat-Bold.ttf");
      // final montserratFont = Font.ttf(montserratFontFile);
      // final Uint8List robotoFontFile =
      //     File('assets/google_fonts/Roboto-Black.ttf').readAsBytesSync();
      // final robotoFont = Font.ttf(robotoFontFile.buffer.asByteData());
      pdf.addPage(Page(
        pageFormat: PdfPageFormat.a4.copyWith(
          marginBottom: 20,
          marginLeft: 20,
          marginRight: 20,
          marginTop: 20,
        ),
        // pageFormat: PdfPageFormat(2480, 3508),
        // orientation: PageOrientation.landscape,
        build: (context) {
          return Stack(fit: StackFit.expand, children: [
            Image(MemoryImage(bgGraphicData), fit: BoxFit.cover),
            Padding(
              padding: const EdgeInsets.all(40.0),
              child: Column(
                children: [
                  Spacer(flex: 2),
                  SizedBox(height: 40),
                  Text(
                    user.name,
                    style: TextStyle(fontSize: 40, font: robotoFont),
                  ),
                  Text(
                    user.email,
                    style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 80),
                  Text(
                    crse.course?.title ?? "",
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 40),
                  Text(
                    'COURSE ID: ${crse.course?.courseId ?? ""}',
                    style: const TextStyle(
                      fontSize: 18,
                    ),
                  ),
                  Row(mainAxisAlignment: MainAxisAlignment.center, children: [
                    Text(
                      "COMPLETED ON ",
                      style: const TextStyle(
                        fontSize: 18,
                      ),
                    ),
                    Text(
                      crse.myCourse.completedOn?.toDate().goodDayDate() ?? "",
                      style: const TextStyle(
                        fontSize: 18,
                      ),
                    ),
                  ]),
                  Row(mainAxisAlignment: MainAxisAlignment.center, children: [
                    Text(
                      "TOTAL WATCH HOURS: ",
                      style: TextStyle(fontSize: 18, font: robotoFont),
                    ),
                    Text(
                      '${crse.course?.duration.hours} Hours ${crse.course?.duration.minutes} Minutes',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.normal,
                      ),
                    ),
                  ]),

                  // const SizedBox(height: 80),
                  Spacer(),
                  Row(
                    children: [
                      Expanded(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            BarcodeWidget(
                                data: "data",
                                barcode: Barcode.qrCode(),
                                width: 80,
                                height: 80),
                          ],
                        ),
                      ),
                      SizedBox(width: 40),
                      Expanded(
                        flex: 2,
                        child: Row(
                          children: [
                            Column(
                              children: [
                                Text(
                                  "September",
                                  style:
                                      TextStyle(fontSize: 20, font: robotoFont),
                                ),
                                Text(
                                  "18",
                                  style: TextStyle(
                                      fontSize: 28,
                                      color: PdfColor.fromHex("006837"),
                                      font: robotoFont),
                                ),
                                Text(
                                  "Saturday",
                                  style:
                                      TextStyle(fontSize: 20, font: robotoFont),
                                ),
                              ],
                            ),
                            Container(
                                margin:
                                    const EdgeInsets.symmetric(horizontal: 12),
                                height: 100,
                                width: 4,
                                color: PdfColor.fromHex("006837")),
                            Expanded(
                              child: Padding(
                                padding: const EdgeInsets.only(right: 40.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      "5:00 PM",
                                      style: TextStyle(
                                          fontSize: 20, font: robotoFont),
                                    ),
                                    Text(
                                      "This is the address of the selected exam center",
                                      style: TextStyle(
                                          fontSize: 14,
                                          color: PdfColors.grey700,
                                          font: robotoFont),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  Spacer(),
                ],
              ),
            ),
          ]);
        },
      ));
      await Printing.sharePdf(
          bytes: await pdf.save(),
          filename: '${DateTime.now().microsecondsSinceEpoch}.pdf');
      return null;
    } catch (e) {
      debugPrint(e.toString());
      return null;
    }
  }
}
